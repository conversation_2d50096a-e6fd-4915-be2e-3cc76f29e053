#!/usr/bin/env node

/**
 * Deployment Verification Script
 * Tests API connectivity and configuration for the deployed application
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: './investor-app-frontend/.env' });
dotenv.config({ path: './investor-app-backend/.env' });

const API_URL = process.env.VITE_API_URL || 'https://archimedes-finance-api-v2.vercel.app';
const FRONTEND_URL = process.env.FRONTEND_URL || 'https://archimedes-finance.pages.dev';

console.log('🚀 Starting deployment verification...\n');

async function testApiHealth() {
  console.log('📡 Testing API health endpoint...');
  try {
    const response = await axios.get(`${API_URL}/health`, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Deployment-Verification-Script'
      }
    });
    
    if (response.status === 200) {
      console.log('✅ API health check passed');
      console.log(`   Status: ${response.data.status}`);
      console.log(`   Uptime: ${Math.floor(response.data.uptime)}s`);
      return true;
    } else {
      console.log(`❌ API health check failed with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ API health check failed: ${error.message}`);
    if (error.code === 'ENOTFOUND') {
      console.log('   DNS resolution failed - check if the API URL is correct');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('   Connection refused - API server may be down');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('   Request timed out - API server may be slow or unreachable');
    }
    return false;
  }
}

async function testApiVersion() {
  console.log('\n📋 Testing API version endpoint...');
  try {
    const response = await axios.get(`${API_URL}/version`, {
      timeout: 10000
    });
    
    if (response.status === 200) {
      console.log('✅ API version endpoint accessible');
      console.log(`   Version: ${response.data.version}`);
      console.log(`   Environment: ${response.data.environment}`);
      return true;
    } else {
      console.log(`❌ API version check failed with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ API version check failed: ${error.message}`);
    return false;
  }
}

async function testCorsConfiguration() {
  console.log('\n🌐 Testing CORS configuration...');
  try {
    const response = await axios.options(`${API_URL}/health`, {
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
      },
      timeout: 10000
    });
    
    const allowedOrigin = response.headers['access-control-allow-origin'];
    const allowedMethods = response.headers['access-control-allow-methods'];
    const allowedHeaders = response.headers['access-control-allow-headers'];
    
    console.log('✅ CORS preflight request successful');
    console.log(`   Allowed Origin: ${allowedOrigin}`);
    console.log(`   Allowed Methods: ${allowedMethods}`);
    console.log(`   Allowed Headers: ${allowedHeaders}`);
    
    if (allowedOrigin === '*' || allowedOrigin === FRONTEND_URL) {
      console.log('✅ CORS origin configuration looks correct');
      return true;
    } else {
      console.log(`⚠️  CORS origin mismatch. Expected: ${FRONTEND_URL}, Got: ${allowedOrigin}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ CORS test failed: ${error.message}`);
    return false;
  }
}

async function testAuthEndpoint() {
  console.log('\n🔐 Testing authentication endpoint...');
  try {
    // Test with invalid credentials to see if endpoint is accessible
    const response = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'invalid'
    }, {
      timeout: 10000,
      validateStatus: (status) => status < 500 // Accept 4xx errors as valid responses
    });
    
    if (response.status === 401) {
      console.log('✅ Auth endpoint accessible (returned expected 401 for invalid credentials)');
      return true;
    } else if (response.status === 400) {
      console.log('✅ Auth endpoint accessible (returned 400 for malformed request)');
      return true;
    } else {
      console.log(`⚠️  Auth endpoint returned unexpected status: ${response.status}`);
      return false;
    }
  } catch (error) {
    if (error.response && error.response.status < 500) {
      console.log('✅ Auth endpoint accessible (returned expected error response)');
      return true;
    }
    console.log(`❌ Auth endpoint test failed: ${error.message}`);
    return false;
  }
}

async function runVerification() {
  console.log(`API URL: ${API_URL}`);
  console.log(`Frontend URL: ${FRONTEND_URL}\n`);
  
  const tests = [
    { name: 'API Health', test: testApiHealth },
    { name: 'API Version', test: testApiVersion },
    { name: 'CORS Configuration', test: testCorsConfiguration },
    { name: 'Auth Endpoint', test: testAuthEndpoint }
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    const result = await test();
    results.push({ name, passed: result });
  }
  
  console.log('\n📊 Verification Summary:');
  console.log('========================');
  
  let allPassed = true;
  for (const { name, passed } of results) {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${name}`);
    if (!passed) allPassed = false;
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (allPassed) {
    console.log('🎉 All tests passed! Deployment appears to be working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the configuration and try again.');
    console.log('\nCommon issues:');
    console.log('- Check if API_URL in frontend .env is correct');
    console.log('- Verify CORS_ALLOWED_ORIGIN in backend .env includes frontend URL');
    console.log('- Ensure backend is deployed and accessible');
    console.log('- Check if environment variables are properly set in deployment platform');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run the verification
runVerification().catch(error => {
  console.error('❌ Verification script failed:', error.message);
  process.exit(1);
});
