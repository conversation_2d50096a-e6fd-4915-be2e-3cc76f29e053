# Comprehensive Testing Guide

This guide provides detailed instructions for using the comprehensive testing framework to systematically test all backend and frontend capabilities of the investor application platform.

## Quick Start

### 1. Setup Framework
```bash
cd test-framework
npm install
npm run setup
```

### 2. Validate Framework
```bash
npm run test:validate-framework
```

### 3. Run All Tests
```bash
npm run test:comprehensive
```

## Test Categories

### Backend API Tests
Tests all REST API endpoints with various scenarios:

```bash
# Run all backend tests
npm run test:backend-api

# Run specific endpoint tests
npm run test:backend-api -- --endpoint auth
npm run test:backend-api -- --endpoint tokenisation
```

**Coverage:**
- Authentication (register, login, refresh, logout)
- Profile management (get, update, image upload)
- Asset tokenization (create, approve, reject, list)
- Client management (search, link, list)
- Notifications (create, list, mark read)
- KYC verification (submit, status)
- Messaging (send, list, mark read)
- Vesting contracts (create, list)
- Reports (generate, list)
- Activity tracking (log, list)

### Frontend Component Tests
Tests all Vue.js components in isolation:

```bash
# Run all frontend tests
npm run test:frontend-components

# Run specific component tests
npm run test:frontend-components -- --component NavBar
npm run test:frontend-components -- --component Dashboard
```

**Coverage:**
- Navigation components (NavBar, ProfileTile)
- Dashboard components (BalanceHistory, AssetSummary)
- Form components (Registration, Login, Profile)
- Modal components (TokeniseAsset, Notifications)
- Chart components (BalanceHistoryApex)
- KYC components (KycVerification)
- Messaging components (MessagesList, ComposeMessage)

### End-to-End User Flow Tests
Tests complete user journeys across the application:

```bash
# Run all E2E tests
npm run test:user-flows

# Run specific flow tests
npm run test:user-flows -- --flow onboarding
npm run test:user-flows -- --flow kyc-verification
```

**Coverage:**
- User onboarding (registration → verification → dashboard)
- KYC verification process (document upload → verification → approval)
- Asset tokenization workflow (creation → approval → portfolio)
- Client-manager relationship (search → link → manage)
- Messaging communication (compose → send → read → reply)
- Dashboard walkthrough (tutorial → completion → re-access)
- Session management (login → extend → expire → logout)

### Integration Tests
Tests frontend-backend integration and data flow:

```bash
# Run all integration tests
npm run test:integration

# Run specific integration tests
npm run test:integration -- --category auth-flow
npm run test:integration -- --category data-sync
```

**Coverage:**
- Authentication flow integration
- Real-time data synchronization
- File upload/download workflows
- API error handling
- State management consistency

## Test Execution Modes

### Comprehensive Testing
```bash
npm run test:comprehensive
```
Runs all test categories with full coverage and detailed reporting.

### Quick Testing
```bash
npm run test:quick
```
Runs essential tests only for rapid feedback during development.

### Smoke Testing
```bash
npm run test:smoke
```
Runs basic functionality tests to verify system health.

### Regression Testing
```bash
npm run test:regression
```
Runs tests focused on previously identified issues and critical paths.

### CI/CD Testing
```bash
npm run test:ci
```
Optimized for continuous integration environments with parallel execution.

## Test Configuration

### Environment Configuration
Edit `config/test-config.js` to customize:

```javascript
export const testConfig = {
  api: {
    baseUrl: 'http://localhost:3000',
    timeout: 30000
  },
  frontend: {
    baseUrl: 'http://localhost:4173',
    timeout: 30000
  },
  execution: {
    parallel: true,
    maxConcurrency: 4,
    retries: 2
  }
};
```

### Test Data Configuration
Customize test data in `config/test-config.js`:

```javascript
testData: {
  users: {
    manager: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      role: 'manager'
    },
    client: {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      role: 'client'
    }
  }
}
```

## Test Reports

### Report Formats
The framework generates multiple report formats:

1. **JSON Report** (`test-results.json`)
   - Machine-readable format for automation
   - Detailed test results and metrics

2. **HTML Report** (`test-report.html`)
   - Human-readable visual report
   - Interactive charts and graphs

3. **Markdown Report** (`test-report.md`)
   - Documentation-friendly format
   - Easy to include in README files

4. **JUnit Report** (`junit-report.xml`)
   - CI/CD integration format
   - Compatible with most CI systems

5. **Coding Agent Report** (`agent-report.json`)
   - Structured format for AI agents
   - Auto-fix suggestions and recommendations

### Report Locations
```
test-framework/reports/
├── latest/           # Latest test results
│   ├── test-report.html
│   ├── test-results.json
│   └── agent-report.json
└── history/          # Historical results
    ├── test-results-2024-01-15T10-30-00.json
    └── test-report-2024-01-15T10-30-00.html
```

## Advanced Features

### Performance Testing
```bash
npm run test:performance
```

Measures and validates:
- API response times
- Frontend load times
- Memory usage
- Throughput capacity

### Security Testing
```bash
npm run test:security
```

Validates:
- Input sanitization
- Authentication security
- Authorization controls
- Data protection

### Cross-Browser Testing
```bash
npm run test:user-flows -- --browsers chromium,firefox,webkit
```

Tests user flows across multiple browsers for compatibility.

### Mobile Testing
```bash
npm run test:user-flows -- --mobile
```

Tests responsive design and mobile-specific functionality.

## Framework Validation

### Self-Validation
```bash
npm run test:validate-framework
```

Checks:
- Configuration completeness
- Endpoint definitions vs. actual codebase
- Test coverage analysis
- Framework integrity

### Auto-Fix Suggestions
```bash
npm run test:auto-fix
```

Analyzes test failures and provides:
- Automatic fix suggestions
- Pattern recognition
- Common issue resolution

## Development Workflow

### 1. Feature Development
When developing new features:

1. Add corresponding test definitions
2. Update user flow tests if needed
3. Run framework validation
4. Execute relevant test categories

### 2. Pre-Commit Testing
```bash
npm run test:quick
```

### 3. Pre-Deployment Testing
```bash
npm run test:comprehensive
```

### 4. Post-Deployment Verification
```bash
npm run test:smoke
```

## Troubleshooting

### Common Issues

**Framework Validation Fails**
- Check configuration file completeness
- Verify API and frontend URLs are accessible
- Ensure test database is available

**Tests Timeout**
- Increase timeout values in configuration
- Check system performance
- Verify network connectivity

**Authentication Failures**
- Verify test user credentials
- Check token expiration settings
- Validate authentication endpoints

**File Upload Tests Fail**
- Check file permissions
- Verify upload directory exists
- Validate file size limits

### Debug Mode
```bash
DEBUG=true npm run test:comprehensive
```

Enables verbose logging and detailed error messages.

### Test Data Cleanup
```bash
npm run clean
```

Removes all test data and resets the test environment.

## Best Practices

### 1. Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

### 2. Test Data Management
- Use unique test data for each test
- Clean up test data after execution
- Avoid dependencies between tests

### 3. Error Handling
- Test both success and failure scenarios
- Validate error messages and status codes
- Handle network timeouts gracefully

### 4. Maintenance
- Run framework validation regularly
- Update test definitions when adding features
- Review and update test data periodically

## Integration with CI/CD

### GitHub Actions Example
```yaml
name: Comprehensive Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: cd test-framework && npm install
      - run: cd test-framework && npm run test:ci
      - uses: actions/upload-artifact@v2
        with:
          name: test-reports
          path: test-framework/reports/latest/
```

### Jenkins Pipeline Example
```groovy
pipeline {
    agent any
    stages {
        stage('Test') {
            steps {
                dir('test-framework') {
                    sh 'npm install'
                    sh 'npm run test:ci'
                }
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'test-framework/reports/latest',
                        reportFiles: 'test-report.html',
                        reportName: 'Test Report'
                    ])
                }
            }
        }
    }
}
```

## Support and Maintenance

### Framework Updates
The framework includes self-updating capabilities:
- Automatic discovery of new endpoints
- Schema validation against current codebase
- Test coverage analysis and recommendations

### Community Contributions
When contributing to the framework:
1. Follow existing patterns and conventions
2. Add tests for new framework features
3. Update documentation
4. Run comprehensive validation before submitting

### Getting Help
- Check the troubleshooting section
- Review test logs and reports
- Run framework validation for diagnostics
- Consult the auto-fix suggestions
