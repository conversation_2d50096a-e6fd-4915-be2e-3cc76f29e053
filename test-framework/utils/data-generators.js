import { v4 as uuidv4 } from 'uuid';
import { testConfig } from '../config/test-config.js';

export class TestDataGenerator {
  constructor() {
    this.counter = 0;
  }

  // Generate unique test data to avoid conflicts
  generateUniqueId() {
    return `test_${Date.now()}_${this.counter++}`;
  }

  generateEmail(prefix = 'test') {
    return `${prefix}_${this.generateUniqueId()}@example.com`;
  }

  generatePhone() {
    return `555${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`;
  }

  generateTaxId() {
    return Math.floor(Math.random() * 1000000000).toString().padStart(9, '0');
  }

  // Generate test data based on endpoint and scenario
  generateForScenario(endpoint, scenario) {
    const generators = {
      // Authentication scenarios
      auth: {
        register: {
          valid_data: () => ({
            role: 'client',
            contactName: `Test User ${this.generateUniqueId()}`,
            email: this.generateEmail('register'),
            password: 'TestPassword123!',
            taxId: this.generateTaxId(),
            phone: this.generatePhone()
          }),
          invalid_email: () => ({
            role: 'client',
            contactName: 'Test User',
            email: 'invalid-email',
            password: 'TestPassword123!',
            taxId: this.generateTaxId(),
            phone: this.generatePhone()
          }),
          weak_password: () => ({
            role: 'client',
            contactName: 'Test User',
            email: this.generateEmail('weak'),
            password: '123',
            taxId: this.generateTaxId(),
            phone: this.generatePhone()
          }),
          duplicate_email: () => ({
            role: 'client',
            contactName: 'Test User',
            email: testConfig.testData.users.client.email,
            password: 'TestPassword123!',
            taxId: this.generateTaxId(),
            phone: this.generatePhone()
          })
        },
        login: {
          valid_credentials: () => ({
            email: testConfig.testData.users.client.email,
            password: testConfig.testData.users.client.password
          }),
          invalid_credentials: () => ({
            email: testConfig.testData.users.client.email,
            password: 'wrongpassword'
          }),
          remember_me: () => ({
            email: testConfig.testData.users.client.email,
            password: testConfig.testData.users.client.password,
            rememberMe: true
          })
        }
      },

      // Profile scenarios
      profile: {
        update: {
          valid_update: () => ({
            contactName: `Updated Name ${this.generateUniqueId()}`,
            phone: this.generatePhone(),
            address: '123 Test Street, Test City'
          }),
          invalid_data: () => ({
            contactName: '', // Invalid empty name
            phone: 'invalid-phone',
            email: 'invalid-email'
          })
        }
      },

      // Asset tokenization scenarios
      tokenisation: {
        create: {
          valid_asset: () => ({
            name: `Test Asset ${this.generateUniqueId()}`,
            type: 'real_estate',
            value: 100000 + Math.floor(Math.random() * 900000),
            ownership_percentage: Math.floor(Math.random() * 100) + 1,
            metadata: {
              description: 'Test asset for automated testing',
              location: 'Test City',
              created_by: 'test_system'
            },
            client_id: 'self'
          }),
          invalid_data: () => ({
            name: '', // Invalid empty name
            type: 'invalid_type',
            value: -1000, // Invalid negative value
            ownership_percentage: 150 // Invalid percentage > 100
          })
        }
      },

      // Client management scenarios
      clients: {
        search: {
          valid_search: () => ({
            query: testConfig.testData.users.client.email
          }),
          empty_results: () => ({
            query: '<EMAIL>'
          })
        },
        link: {
          valid_link: () => ({
            clientEmail: testConfig.testData.users.client.email
          }),
          invalid_client: () => ({
            clientEmail: '<EMAIL>'
          })
        }
      },

      // Notification scenarios
      notifications: {
        create: {
          valid_notification: () => ({
            recipientId: testConfig.testData.users.client.id,
            type: 'info',
            message: `Test notification ${this.generateUniqueId()}`
          }),
          invalid_data: () => ({
            recipientId: 'invalid-id',
            type: 'invalid-type',
            message: ''
          })
        }
      },

      // Messaging scenarios
      messages: {
        send: {
          valid_message: () => ({
            subject: `Test Message ${this.generateUniqueId()}`,
            body: 'This is a test message for automated testing purposes.',
            recipientIds: [testConfig.testData.users.client.id]
          }),
          broadcast_message: () => ({
            subject: `Broadcast Message ${this.generateUniqueId()}`,
            body: 'This is a broadcast test message.',
            recipientIds: [testConfig.testData.users.client.id],
            isBroadcast: true
          }),
          sensitive_content: () => ({
            subject: 'Sensitive Information Test',
            body: 'This message contains SSN: *********** and credit card: 4111-1111-1111-1111',
            recipientIds: [testConfig.testData.users.client.id]
          })
        }
      },

      // KYC scenarios
      kyc: {
        verify: {
          valid_documents: () => ({
            // This would include file uploads in a real scenario
            documentType: 'passport',
            documentNumber: `P${this.generateUniqueId()}`,
            expiryDate: '2030-12-31'
          }),
          invalid_documents: () => ({
            documentType: 'invalid',
            documentNumber: '',
            expiryDate: '2020-01-01' // Expired
          })
        }
      },

      // Vesting scenarios
      vesting: {
        create: {
          valid_contract: () => ({
            clientId: testConfig.testData.users.client.id,
            assetId: uuidv4(),
            vestingSchedule: {
              startDate: new Date().toISOString(),
              endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
              cliffPeriod: 90,
              vestingPeriod: 365
            },
            totalAmount: 100000
          }),
          invalid_data: () => ({
            clientId: 'invalid-id',
            assetId: '',
            vestingSchedule: {
              startDate: 'invalid-date',
              endDate: '2020-01-01', // End before start
              cliffPeriod: -30, // Negative cliff
              vestingPeriod: 0 // Zero vesting period
            },
            totalAmount: -1000 // Negative amount
          })
        }
      }
    };

    // Navigate to the appropriate generator
    const categoryGenerators = generators[endpoint.category];
    if (!categoryGenerators) {
      return this.generateDefaultData(endpoint, scenario);
    }

    const endpointGenerators = categoryGenerators[endpoint.name];
    if (!endpointGenerators) {
      return this.generateDefaultData(endpoint, scenario);
    }

    const scenarioGenerator = endpointGenerators[scenario];
    if (!scenarioGenerator) {
      return this.generateDefaultData(endpoint, scenario);
    }

    return scenarioGenerator();
  }

  generateDefaultData(endpoint, scenario) {
    // Fallback generator for scenarios not explicitly defined
    if (scenario.includes('invalid')) {
      return {
        invalid: true,
        scenario: scenario,
        timestamp: new Date().toISOString()
      };
    }

    return {
      test: true,
      scenario: scenario,
      id: this.generateUniqueId(),
      timestamp: new Date().toISOString()
    };
  }

  // Generate test files for upload scenarios
  generateTestFile(type = 'image') {
    const files = {
      image: {
        name: `test_image_${this.generateUniqueId()}.jpg`,
        type: 'image/jpeg',
        size: 1024 * 100, // 100KB
        content: 'base64_encoded_image_data_here'
      },
      document: {
        name: `test_document_${this.generateUniqueId()}.pdf`,
        type: 'application/pdf',
        size: 1024 * 500, // 500KB
        content: 'base64_encoded_pdf_data_here'
      },
      oversized: {
        name: `oversized_file_${this.generateUniqueId()}.jpg`,
        type: 'image/jpeg',
        size: 1024 * 1024 * 10, // 10MB
        content: 'base64_encoded_large_image_data_here'
      }
    };

    return files[type] || files.image;
  }

  // Generate bulk test data for performance testing
  generateBulkData(count, generator) {
    const data = [];
    for (let i = 0; i < count; i++) {
      data.push(generator());
    }
    return data;
  }

  // Generate realistic financial data
  generateFinancialData() {
    return {
      balance: Math.floor(Math.random() * 1000000) + 10000,
      currency: 'USD',
      lastUpdated: new Date().toISOString(),
      transactions: this.generateTransactionHistory(10)
    };
  }

  generateTransactionHistory(count) {
    const transactions = [];
    for (let i = 0; i < count; i++) {
      transactions.push({
        id: uuidv4(),
        amount: Math.floor(Math.random() * 10000) + 100,
        type: Math.random() > 0.5 ? 'credit' : 'debit',
        description: `Test transaction ${i + 1}`,
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
      });
    }
    return transactions.sort((a, b) => new Date(b.date) - new Date(a.date));
  }

  // Clean up generated test data
  getGeneratedEmails() {
    // Return list of generated emails for cleanup
    return this.generatedEmails || [];
  }

  getGeneratedIds() {
    // Return list of generated IDs for cleanup
    return this.generatedIds || [];
  }
}
