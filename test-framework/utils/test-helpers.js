import fs from 'fs-extra';
import path from 'path';
import { testConfig } from '../config/test-config.js';

export class TestHelpers {
  constructor() {
    this.cleanupTasks = [];
  }

  // Utility functions
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  generateRandomString(length = 10) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  generateRandomEmail() {
    return `test_${this.generateRandomString(8)}@example.com`;
  }

  // Test data management
  async createTestUser(role = 'client') {
    const userData = {
      email: this.generateRandomEmail(),
      password: 'TestPassword123!',
      role: role,
      contactName: `Test ${role} ${this.generateRandomString(5)}`,
      taxId: Math.floor(Math.random() * 1000000000).toString(),
      phone: `555${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`
    };

    // Register cleanup task
    this.cleanupTasks.push({
      type: 'delete_user',
      email: userData.email
    });

    return userData;
  }

  async createTestAsset(managerId) {
    const assetData = {
      name: `Test Asset ${this.generateRandomString(5)}`,
      type: 'real_estate',
      value: Math.floor(Math.random() * 1000000) + 100000,
      ownership_percentage: Math.floor(Math.random() * 100) + 1,
      metadata: {
        description: 'Test asset for automated testing',
        location: 'Test City',
        created_by: 'test_framework'
      },
      client_id: managerId
    };

    // Register cleanup task
    this.cleanupTasks.push({
      type: 'delete_asset',
      name: assetData.name
    });

    return assetData;
  }

  // File handling utilities
  async createTestFile(type = 'image', size = 'small') {
    const sizes = {
      small: 1024 * 10,   // 10KB
      medium: 1024 * 100, // 100KB
      large: 1024 * 1024  // 1MB
    };

    const fileData = {
      name: `test_${type}_${this.generateRandomString(8)}.${type === 'image' ? 'jpg' : 'pdf'}`,
      type: type === 'image' ? 'image/jpeg' : 'application/pdf',
      size: sizes[size] || sizes.small,
      content: this.generateTestFileContent(type, sizes[size] || sizes.small)
    };

    return fileData;
  }

  generateTestFileContent(type, size) {
    // Generate mock file content
    if (type === 'image') {
      return 'data:image/jpeg;base64,' + Buffer.alloc(size, 'A').toString('base64');
    } else {
      return 'data:application/pdf;base64,' + Buffer.alloc(size, 'B').toString('base64');
    }
  }

  // Database utilities
  async cleanupTestData() {
    console.log(`Cleaning up ${this.cleanupTasks.length} test items...`);
    
    for (const task of this.cleanupTasks) {
      try {
        await this.executeCleanupTask(task);
      } catch (error) {
        console.warn(`Cleanup task failed:`, task, error.message);
      }
    }

    this.cleanupTasks = [];
  }

  async executeCleanupTask(task) {
    switch (task.type) {
      case 'delete_user':
        // In a real implementation, this would delete the test user
        console.log(`Would delete test user: ${task.email}`);
        break;
      case 'delete_asset':
        // In a real implementation, this would delete the test asset
        console.log(`Would delete test asset: ${task.name}`);
        break;
      case 'delete_message':
        // In a real implementation, this would delete the test message
        console.log(`Would delete test message: ${task.id}`);
        break;
      default:
        console.warn(`Unknown cleanup task type: ${task.type}`);
    }
  }

  // Validation utilities
  validateResponse(response, expectedSchema) {
    const errors = [];

    if (!response) {
      errors.push('Response is null or undefined');
      return { valid: false, errors };
    }

    if (expectedSchema.status && response.status !== expectedSchema.status) {
      errors.push(`Expected status ${expectedSchema.status}, got ${response.status}`);
    }

    if (expectedSchema.properties) {
      for (const [property, rules] of Object.entries(expectedSchema.properties)) {
        const value = response.data?.[property];
        
        if (rules.required && (value === undefined || value === null)) {
          errors.push(`Required property '${property}' is missing`);
          continue;
        }

        if (value !== undefined && rules.type && typeof value !== rules.type) {
          errors.push(`Property '${property}' should be ${rules.type}, got ${typeof value}`);
        }

        if (rules.format === 'email' && value && !this.isValidEmail(value)) {
          errors.push(`Property '${property}' should be a valid email`);
        }

        if (rules.minLength && value && value.length < rules.minLength) {
          errors.push(`Property '${property}' should be at least ${rules.minLength} characters`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Performance measurement utilities
  async measurePerformance(testFunction, iterations = 1) {
    const results = [];
    
    for (let i = 0; i < iterations; i++) {
      const startTime = process.hrtime.bigint();
      const startMemory = process.memoryUsage();
      
      try {
        await testFunction();
        
        const endTime = process.hrtime.bigint();
        const endMemory = process.memoryUsage();
        
        results.push({
          iteration: i + 1,
          duration: Number(endTime - startTime) / 1000000, // Convert to milliseconds
          memoryDelta: {
            rss: endMemory.rss - startMemory.rss,
            heapUsed: endMemory.heapUsed - startMemory.heapUsed,
            heapTotal: endMemory.heapTotal - startMemory.heapTotal
          },
          success: true
        });
      } catch (error) {
        const endTime = process.hrtime.bigint();
        
        results.push({
          iteration: i + 1,
          duration: Number(endTime - startTime) / 1000000,
          error: error.message,
          success: false
        });
      }
    }

    return {
      iterations,
      results,
      summary: this.calculatePerformanceSummary(results)
    };
  }

  calculatePerformanceSummary(results) {
    const successfulResults = results.filter(r => r.success);
    
    if (successfulResults.length === 0) {
      return { error: 'No successful iterations' };
    }

    const durations = successfulResults.map(r => r.duration);
    const memoryUsages = successfulResults.map(r => r.memoryDelta.heapUsed);

    return {
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      averageMemoryUsage: memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length,
      successRate: (successfulResults.length / results.length) * 100
    };
  }

  // Retry utilities
  async retryOperation(operation, maxRetries = 3, delay = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          throw new Error(`Operation failed after ${maxRetries} attempts: ${error.message}`);
        }
        
        console.log(`Attempt ${attempt} failed, retrying in ${delay}ms...`);
        await this.delay(delay);
        delay *= 2; // Exponential backoff
      }
    }
  }

  // Environment utilities
  async checkEnvironmentHealth() {
    const health = {
      backend: false,
      frontend: false,
      database: false,
      timestamp: new Date()
    };

    try {
      // Check backend health
      const backendResponse = await fetch(`${testConfig.api.baseUrl}/health`);
      health.backend = backendResponse.ok;
    } catch (error) {
      console.warn('Backend health check failed:', error.message);
    }

    try {
      // Check frontend accessibility
      const frontendResponse = await fetch(testConfig.frontend.baseUrl);
      health.frontend = frontendResponse.ok;
    } catch (error) {
      console.warn('Frontend health check failed:', error.message);
    }

    // Database health would be checked through backend API
    health.database = health.backend;

    return health;
  }

  // Logging utilities
  logTestStep(step, details = {}) {
    if (testConfig.execution.verbose) {
      console.log(`[TEST STEP] ${step}`, details);
    }
  }

  logTestResult(testName, passed, duration, error = null) {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${testName} (${duration}ms)${error ? ` - ${error}` : ''}`);
  }

  // Screenshot utilities (for E2E tests)
  async captureScreenshot(page, name) {
    if (testConfig.reporting.includeScreenshots) {
      const screenshotPath = path.join(testConfig.reporting.outputDir, 'screenshots', `${name}-${Date.now()}.png`);
      await fs.ensureDir(path.dirname(screenshotPath));
      
      // In a real implementation, this would capture a screenshot
      console.log(`Would capture screenshot: ${screenshotPath}`);
      return screenshotPath;
    }
    return null;
  }
}
