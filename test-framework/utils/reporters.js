import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { testConfig } from '../config/test-config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class ReportGenerator {
  constructor() {
    this.outputDir = testConfig.reporting.outputDir;
    this.ensureOutputDirectory();
  }

  async ensureOutputDirectory() {
    await fs.ensureDir(this.outputDir);
    await fs.ensureDir(path.join(this.outputDir, 'latest'));
    await fs.ensureDir(path.join(this.outputDir, 'history'));
  }

  async generateComprehensiveReport(results) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Generate different report formats
    await Promise.all([
      this.generateJSONReport(results, timestamp),
      this.generateHTMLReport(results, timestamp),
      this.generateJUnitReport(results, timestamp),
      this.generateMarkdownReport(results, timestamp),
      this.generateCodingAgentReport(results, timestamp)
    ]);

    // Update latest reports
    await this.updateLatestReports(results, timestamp);

    // Generate trend analysis if historical data exists
    await this.generateTrendReport();
  }

  async generateJSONReport(results, timestamp) {
    const report = {
      metadata: {
        timestamp: results.startTime,
        duration: results.duration,
        framework_version: '1.0.0',
        environment: testConfig.environment
      },
      summary: {
        total_tests: results.totalTests,
        passed: results.passedTests,
        failed: results.failedTests,
        skipped: results.skippedTests,
        success_rate: Math.round((results.passedTests / results.totalTests) * 100),
        categories: Object.keys(results.categories)
      },
      categories: results.categories,
      errors: results.errors,
      warnings: results.warnings
    };

    const filePath = path.join(this.outputDir, 'history', `test-results-${timestamp}.json`);
    await fs.writeJSON(filePath, report, { spaces: 2 });
    
    return filePath;
  }

  async generateHTMLReport(results, timestamp) {
    const html = this.generateHTMLContent(results);
    const filePath = path.join(this.outputDir, 'history', `test-report-${timestamp}.html`);
    await fs.writeFile(filePath, html);
    
    return filePath;
  }

  generateHTMLContent(results) {
    const successRate = Math.round((results.passedTests / results.totalTests) * 100);
    const duration = Math.round(results.duration / 1000);

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .metric h3 { margin: 0 0 10px 0; color: #333; }
        .metric .value { font-size: 2em; font-weight: bold; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .category { margin-bottom: 30px; }
        .category h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-list { background: #f8f9fa; padding: 15px; border-radius: 8px; }
        .test-item { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .test-passed { background: #d4edda; border-left: 4px solid #28a745; }
        .test-failed { background: #f8d7da; border-left: 4px solid #dc3545; }
        .error-section { background: #f8d7da; padding: 15px; border-radius: 8px; margin-top: 20px; }
        .warning-section { background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Comprehensive Test Report</h1>
            <p>Generated on ${results.startTime.toLocaleString()}</p>
            <p>Duration: ${duration} seconds</p>
        </div>

        <div class="summary">
            <div class="metric">
                <h3>Success Rate</h3>
                <div class="value ${successRate >= 80 ? 'passed' : 'failed'}">${successRate}%</div>
            </div>
            <div class="metric">
                <h3>Total Tests</h3>
                <div class="value">${results.totalTests}</div>
            </div>
            <div class="metric">
                <h3>Passed</h3>
                <div class="value passed">${results.passedTests}</div>
            </div>
            <div class="metric">
                <h3>Failed</h3>
                <div class="value failed">${results.failedTests}</div>
            </div>
            <div class="metric">
                <h3>Skipped</h3>
                <div class="value skipped">${results.skippedTests}</div>
            </div>
        </div>

        ${this.generateCategoryHTML(results.categories)}

        ${results.errors.length > 0 ? this.generateErrorsHTML(results.errors) : ''}
        ${results.warnings.length > 0 ? this.generateWarningsHTML(results.warnings) : ''}
    </div>
</body>
</html>`;
  }

  generateCategoryHTML(categories) {
    return Object.entries(categories).map(([name, category]) => `
        <div class="category">
            <h2>${name.charAt(0).toUpperCase() + name.slice(1)} Tests</h2>
            <div class="test-list">
                <p><strong>Summary:</strong> ${category.passed}/${category.total} passed</p>
                ${category.tests ? category.tests.map(test => `
                    <div class="test-item ${test.passed ? 'test-passed' : 'test-failed'}">
                        <strong>${test.name}</strong>
                        <span style="float: right;">${test.duration}ms</span>
                        ${test.error ? `<br><small>Error: ${test.error}</small>` : ''}
                    </div>
                `).join('') : ''}
            </div>
        </div>
    `).join('');
  }

  generateErrorsHTML(errors) {
    return `
        <div class="error-section">
            <h3>Errors (${errors.length})</h3>
            ${errors.map(error => `
                <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 4px;">
                    <strong>${error.category || 'General'}</strong>: ${error.error || error.message}
                    ${error.timestamp ? `<br><small>Time: ${new Date(error.timestamp).toLocaleString()}</small>` : ''}
                </div>
            `).join('')}
        </div>
    `;
  }

  generateWarningsHTML(warnings) {
    return `
        <div class="warning-section">
            <h3>Warnings (${warnings.length})</h3>
            ${warnings.map(warning => `
                <div style="margin: 5px 0;">${warning}</div>
            `).join('')}
        </div>
    `;
  }

  async generateJUnitReport(results, timestamp) {
    const xml = this.generateJUnitXML(results);
    const filePath = path.join(this.outputDir, 'history', `junit-report-${timestamp}.xml`);
    await fs.writeFile(filePath, xml);
    
    return filePath;
  }

  generateJUnitXML(results) {
    const duration = results.duration / 1000;
    
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Comprehensive Test Suite" tests="${results.totalTests}" failures="${results.failedTests}" time="${duration}">`;

    for (const [categoryName, category] of Object.entries(results.categories)) {
      xml += `
  <testsuite name="${categoryName}" tests="${category.total}" failures="${category.failed}" time="${(category.duration || 0) / 1000}">`;
      
      if (category.tests) {
        for (const test of category.tests) {
          xml += `
    <testcase name="${test.name}" time="${(test.duration || 0) / 1000}">`;
          
          if (!test.passed && test.error) {
            xml += `
      <failure message="${test.error}">${test.error}</failure>`;
          }
          
          xml += `
    </testcase>`;
        }
      }
      
      xml += `
  </testsuite>`;
    }

    xml += `
</testsuites>`;

    return xml;
  }

  async generateMarkdownReport(results, timestamp) {
    const markdown = this.generateMarkdownContent(results);
    const filePath = path.join(this.outputDir, 'history', `test-report-${timestamp}.md`);
    await fs.writeFile(filePath, markdown);
    
    return filePath;
  }

  generateMarkdownContent(results) {
    const successRate = Math.round((results.passedTests / results.totalTests) * 100);
    const duration = Math.round(results.duration / 1000);

    return `# Comprehensive Test Report

**Generated:** ${results.startTime.toLocaleString()}  
**Duration:** ${duration} seconds  
**Success Rate:** ${successRate}%

## Summary

| Metric | Value |
|--------|-------|
| Total Tests | ${results.totalTests} |
| Passed | ${results.passedTests} |
| Failed | ${results.failedTests} |
| Skipped | ${results.skippedTests} |

## Test Categories

${Object.entries(results.categories).map(([name, category]) => `
### ${name.charAt(0).toUpperCase() + name.slice(1)}

- **Total:** ${category.total}
- **Passed:** ${category.passed}
- **Failed:** ${category.failed}
- **Success Rate:** ${Math.round((category.passed / category.total) * 100)}%

${category.tests ? category.tests.map(test => 
  `- ${test.passed ? '✅' : '❌'} ${test.name} (${test.duration}ms)${test.error ? `\n  - Error: ${test.error}` : ''}`
).join('\n') : ''}
`).join('\n')}

${results.errors.length > 0 ? `## Errors

${results.errors.map(error => `- **${error.category || 'General'}:** ${error.error || error.message}`).join('\n')}` : ''}

${results.warnings.length > 0 ? `## Warnings

${results.warnings.map(warning => `- ${warning}`).join('\n')}` : ''}
`;
  }

  async generateCodingAgentReport(results, timestamp) {
    // Generate a structured report specifically for coding agents to parse and act upon
    const agentReport = {
      test_execution: {
        timestamp: results.startTime,
        duration_ms: results.duration,
        success_rate: Math.round((results.passedTests / results.totalTests) * 100),
        total_tests: results.totalTests,
        results: {
          passed: results.passedTests,
          failed: results.failedTests,
          skipped: results.skippedTests
        }
      },
      failures: this.extractFailuresForAgent(results),
      recommendations: this.generateAgentRecommendations(results),
      auto_fix_suggestions: this.generateAutoFixSuggestions(results),
      next_actions: this.generateNextActions(results)
    };

    const filePath = path.join(this.outputDir, 'history', `agent-report-${timestamp}.json`);
    await fs.writeJSON(filePath, agentReport, { spaces: 2 });
    
    return filePath;
  }

  extractFailuresForAgent(results) {
    const failures = [];
    
    for (const [category, categoryResults] of Object.entries(results.categories)) {
      if (categoryResults.tests) {
        for (const test of categoryResults.tests) {
          if (!test.passed) {
            failures.push({
              category,
              test_name: test.name,
              error_message: test.error,
              duration: test.duration,
              severity: this.categorizeFailureSeverity(test),
              suggested_fix: this.suggestFix(test)
            });
          }
        }
      }
    }
    
    return failures;
  }

  categorizeFailureSeverity(test) {
    if (test.error && test.error.includes('timeout')) return 'high';
    if (test.error && test.error.includes('500')) return 'high';
    if (test.error && test.error.includes('404')) return 'medium';
    if (test.error && test.error.includes('400')) return 'low';
    return 'medium';
  }

  suggestFix(test) {
    const error = test.error || '';
    
    if (error.includes('timeout')) {
      return 'Increase timeout values or optimize performance';
    }
    if (error.includes('404')) {
      return 'Check if endpoint exists and is properly configured';
    }
    if (error.includes('401') || error.includes('403')) {
      return 'Verify authentication and authorization logic';
    }
    if (error.includes('500')) {
      return 'Check server logs for internal errors';
    }
    
    return 'Review test implementation and expected behavior';
  }

  generateAgentRecommendations(results) {
    const recommendations = [];
    
    if (results.failedTests > 0) {
      recommendations.push('Fix failing tests before deploying to production');
    }
    
    if (results.warnings.length > 5) {
      recommendations.push('Address validation warnings to improve test reliability');
    }
    
    const successRate = (results.passedTests / results.totalTests) * 100;
    if (successRate < 80) {
      recommendations.push('Success rate below 80% - investigate systematic issues');
    }
    
    return recommendations;
  }

  generateAutoFixSuggestions(results) {
    const suggestions = [];
    
    // Analyze patterns in failures to suggest automatic fixes
    const failures = this.extractFailuresForAgent(results);
    const errorPatterns = {};
    
    for (const failure of failures) {
      const pattern = this.extractErrorPattern(failure.error_message);
      errorPatterns[pattern] = (errorPatterns[pattern] || 0) + 1;
    }
    
    for (const [pattern, count] of Object.entries(errorPatterns)) {
      if (count > 1) {
        suggestions.push({
          pattern,
          occurrences: count,
          suggested_action: this.getActionForPattern(pattern)
        });
      }
    }
    
    return suggestions;
  }

  extractErrorPattern(errorMessage) {
    if (!errorMessage) return 'unknown';
    
    if (errorMessage.includes('timeout')) return 'timeout';
    if (errorMessage.includes('404')) return 'not_found';
    if (errorMessage.includes('401')) return 'unauthorized';
    if (errorMessage.includes('500')) return 'server_error';
    
    return 'other';
  }

  getActionForPattern(pattern) {
    const actions = {
      timeout: 'Review and increase timeout configurations',
      not_found: 'Verify endpoint URLs and routing configuration',
      unauthorized: 'Check authentication token generation and validation',
      server_error: 'Review server logs and error handling',
      other: 'Manual investigation required'
    };
    
    return actions[pattern] || actions.other;
  }

  generateNextActions(results) {
    const actions = [];
    
    if (results.failedTests > 0) {
      actions.push({
        priority: 'high',
        action: 'investigate_failures',
        description: 'Investigate and fix failing tests'
      });
    }
    
    if (results.warnings.length > 0) {
      actions.push({
        priority: 'medium',
        action: 'address_warnings',
        description: 'Review and address validation warnings'
      });
    }
    
    actions.push({
      priority: 'low',
      action: 'update_documentation',
      description: 'Update test documentation with latest results'
    });
    
    return actions;
  }

  async updateLatestReports(results, timestamp) {
    const latestDir = path.join(this.outputDir, 'latest');
    
    // Copy reports to latest directory
    const historyDir = path.join(this.outputDir, 'history');
    const files = [
      `test-results-${timestamp}.json`,
      `test-report-${timestamp}.html`,
      `test-report-${timestamp}.md`,
      `agent-report-${timestamp}.json`
    ];
    
    for (const file of files) {
      const srcPath = path.join(historyDir, file);
      const destPath = path.join(latestDir, file.replace(`-${timestamp}`, ''));
      
      if (await fs.pathExists(srcPath)) {
        await fs.copy(srcPath, destPath);
      }
    }
  }

  async generateTrendReport() {
    try {
      const historyDir = path.join(this.outputDir, 'history');
      const files = await fs.readdir(historyDir);
      const resultFiles = files.filter(f => f.startsWith('test-results-') && f.endsWith('.json'));
      
      if (resultFiles.length < 2) {
        return; // Need at least 2 data points for trends
      }
      
      const historicalData = [];
      for (const file of resultFiles.slice(-10)) { // Last 10 runs
        const data = await fs.readJSON(path.join(historyDir, file));
        historicalData.push(data);
      }
      
      const trendReport = this.analyzeTrends(historicalData);
      await fs.writeJSON(path.join(this.outputDir, 'latest', 'trend-report.json'), trendReport, { spaces: 2 });
      
    } catch (error) {
      console.warn('Could not generate trend report:', error.message);
    }
  }

  analyzeTrends(historicalData) {
    const trends = {
      success_rate_trend: this.calculateTrend(historicalData.map(d => d.summary.success_rate)),
      test_count_trend: this.calculateTrend(historicalData.map(d => d.summary.total_tests)),
      duration_trend: this.calculateTrend(historicalData.map(d => d.metadata.duration)),
      failure_patterns: this.analyzeFailurePatterns(historicalData)
    };
    
    return {
      analysis_date: new Date(),
      data_points: historicalData.length,
      trends,
      recommendations: this.generateTrendRecommendations(trends)
    };
  }

  calculateTrend(values) {
    if (values.length < 2) return 'insufficient_data';
    
    const recent = values.slice(-3).reduce((a, b) => a + b, 0) / Math.min(3, values.length);
    const older = values.slice(0, -3).reduce((a, b) => a + b, 0) / Math.max(1, values.length - 3);
    
    const change = ((recent - older) / older) * 100;
    
    if (Math.abs(change) < 5) return 'stable';
    return change > 0 ? 'improving' : 'declining';
  }

  analyzeFailurePatterns(historicalData) {
    const patterns = {};
    
    for (const data of historicalData) {
      for (const error of data.errors || []) {
        const pattern = this.extractErrorPattern(error.error || error.message);
        patterns[pattern] = (patterns[pattern] || 0) + 1;
      }
    }
    
    return patterns;
  }

  generateTrendRecommendations(trends) {
    const recommendations = [];
    
    if (trends.success_rate_trend === 'declining') {
      recommendations.push('Success rate is declining - investigate recent changes');
    }
    
    if (trends.duration_trend === 'declining') {
      recommendations.push('Test duration is increasing - consider performance optimization');
    }
    
    return recommendations;
  }
}
