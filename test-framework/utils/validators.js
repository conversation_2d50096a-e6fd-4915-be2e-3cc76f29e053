import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { testConfig } from '../config/test-config.js';
import { endpoints, getAllEndpoints } from '../config/endpoints.js';
import { userFlows } from '../config/user-flows.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class TestValidator {
  constructor() {
    this.warnings = [];
    this.errors = [];
    this.codebaseInfo = null;
  }

  async validateFramework() {
    this.warnings = [];
    this.errors = [];

    // Validate test configuration
    await this.validateTestConfiguration();

    // Validate endpoint definitions against actual codebase
    await this.validateEndpointDefinitions();

    // Validate user flow definitions
    await this.validateUserFlowDefinitions();

    // Validate test coverage
    await this.validateTestCoverage();

    // Validate test data integrity
    await this.validateTestDataIntegrity();

    return {
      passed: this.errors.length === 0,
      warnings: this.warnings,
      errors: this.errors,
      timestamp: new Date()
    };
  }

  async validateTestConfiguration() {
    try {
      // Check required configuration values
      const requiredPaths = [
        'api.baseUrl',
        'frontend.baseUrl',
        'database.testUrl',
        'testData.users.manager',
        'testData.users.client'
      ];

      for (const configPath of requiredPaths) {
        const value = this.getNestedValue(testConfig, configPath);
        if (!value) {
          this.errors.push(`Missing required configuration: ${configPath}`);
        }
      }

      // Validate URL formats
      if (testConfig.api.baseUrl && !this.isValidUrl(testConfig.api.baseUrl)) {
        this.errors.push('Invalid API base URL format');
      }

      if (testConfig.frontend.baseUrl && !this.isValidUrl(testConfig.frontend.baseUrl)) {
        this.errors.push('Invalid frontend base URL format');
      }

    } catch (error) {
      this.errors.push(`Configuration validation error: ${error.message}`);
    }
  }

  async validateEndpointDefinitions() {
    try {
      // Get actual backend routes from codebase
      const actualRoutes = await this.discoverBackendRoutes();
      const definedEndpoints = getAllEndpoints();

      // Check for missing endpoint definitions
      for (const route of actualRoutes) {
        const found = definedEndpoints.find(ep => 
          ep.method === route.method && ep.path === route.path
        );
        
        if (!found) {
          this.warnings.push(`Endpoint not defined in test config: ${route.method} ${route.path}`);
        }
      }

      // Check for outdated endpoint definitions
      for (const endpoint of definedEndpoints) {
        const found = actualRoutes.find(route => 
          route.method === endpoint.method && route.path === endpoint.path
        );
        
        if (!found) {
          this.warnings.push(`Endpoint definition may be outdated: ${endpoint.method} ${endpoint.path}`);
        }
      }

    } catch (error) {
      this.warnings.push(`Could not validate endpoint definitions: ${error.message}`);
    }
  }

  async validateUserFlowDefinitions() {
    try {
      // Get actual frontend components and routes
      const frontendComponents = await this.discoverFrontendComponents();
      const frontendRoutes = await this.discoverFrontendRoutes();

      // Validate that flow steps reference existing components/routes
      for (const [flowName, flow] of Object.entries(userFlows)) {
        for (const step of flow.steps) {
          if (step.action === 'navigate' && step.target.startsWith('/')) {
            const routeExists = frontendRoutes.some(route => route.path === step.target);
            if (!routeExists) {
              this.warnings.push(`Flow ${flowName} references non-existent route: ${step.target}`);
            }
          }

          if (step.target && step.target.includes('data-testid')) {
            // Extract testid and check if it exists in components
            const testId = step.target.match(/data-testid="([^"]+)"/)?.[1];
            if (testId && !this.testIdExistsInComponents(testId, frontendComponents)) {
              this.warnings.push(`Flow ${flowName} references non-existent test ID: ${testId}`);
            }
          }
        }
      }

    } catch (error) {
      this.warnings.push(`Could not validate user flow definitions: ${error.message}`);
    }
  }

  async validateTestCoverage() {
    try {
      // Check API endpoint coverage
      const allEndpoints = getAllEndpoints();
      const testedEndpoints = allEndpoints.filter(ep => ep.testScenarios && ep.testScenarios.length > 0);
      const coveragePercentage = (testedEndpoints.length / allEndpoints.length) * 100;

      if (coveragePercentage < 80) {
        this.warnings.push(`API endpoint test coverage is ${coveragePercentage.toFixed(1)}% (recommended: 80%+)`);
      }

      // Check user flow coverage
      const criticalFlows = ['userOnboarding', 'kycVerification', 'assetTokenization'];
      for (const flow of criticalFlows) {
        if (!userFlows[flow]) {
          this.errors.push(`Missing critical user flow: ${flow}`);
        }
      }

    } catch (error) {
      this.warnings.push(`Could not validate test coverage: ${error.message}`);
    }
  }

  async validateTestDataIntegrity() {
    try {
      // Validate test user data
      const users = testConfig.testData.users;
      for (const [role, userData] of Object.entries(users)) {
        if (!userData.email || !userData.password) {
          this.errors.push(`Incomplete test user data for role: ${role}`);
        }

        if (!this.isValidEmail(userData.email)) {
          this.errors.push(`Invalid email format for test user: ${role}`);
        }
      }

      // Validate test asset data
      const assets = testConfig.testData.assets;
      if (assets.sample) {
        if (!assets.sample.name || !assets.sample.type || !assets.sample.value) {
          this.errors.push('Incomplete test asset data');
        }
      }

    } catch (error) {
      this.warnings.push(`Could not validate test data integrity: ${error.message}`);
    }
  }

  async discoverBackendRoutes() {
    try {
      // Read backend route files and extract route definitions
      const backendPath = path.resolve(__dirname, '../../investor-app-backend/src/routes');
      const routeFiles = await fs.readdir(backendPath);
      const routes = [];

      for (const file of routeFiles) {
        if (file.endsWith('.js')) {
          const filePath = path.join(backendPath, file);
          const content = await fs.readFile(filePath, 'utf8');
          
          // Extract route definitions using regex
          const routeMatches = content.matchAll(/router\.(get|post|put|delete|patch)\s*\(\s*['"`]([^'"`]+)['"`]/g);
          
          for (const match of routeMatches) {
            routes.push({
              method: match[1].toUpperCase(),
              path: match[2],
              file: file
            });
          }
        }
      }

      return routes;
    } catch (error) {
      throw new Error(`Failed to discover backend routes: ${error.message}`);
    }
  }

  async discoverFrontendComponents() {
    try {
      // Read frontend component files
      const componentsPath = path.resolve(__dirname, '../../investor-app-frontend/src/components');
      const components = [];

      const scanDirectory = async (dir) => {
        const items = await fs.readdir(dir);
        
        for (const item of items) {
          const itemPath = path.join(dir, item);
          const stat = await fs.stat(itemPath);
          
          if (stat.isDirectory()) {
            await scanDirectory(itemPath);
          } else if (item.endsWith('.vue')) {
            const content = await fs.readFile(itemPath, 'utf8');
            components.push({
              name: item,
              path: itemPath,
              content: content
            });
          }
        }
      };

      await scanDirectory(componentsPath);
      return components;
    } catch (error) {
      throw new Error(`Failed to discover frontend components: ${error.message}`);
    }
  }

  async discoverFrontendRoutes() {
    try {
      // Read router configuration
      const routerPath = path.resolve(__dirname, '../../investor-app-frontend/src/router/index.js');
      const content = await fs.readFile(routerPath, 'utf8');
      
      // Extract route definitions
      const routeMatches = content.matchAll(/path:\s*['"`]([^'"`]+)['"`]/g);
      const routes = [];
      
      for (const match of routeMatches) {
        routes.push({
          path: match[1]
        });
      }

      return routes;
    } catch (error) {
      throw new Error(`Failed to discover frontend routes: ${error.message}`);
    }
  }

  testIdExistsInComponents(testId, components) {
    return components.some(component => 
      component.content.includes(`data-testid="${testId}"`)
    );
  }

  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch {
      return false;
    }
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Generate validation report
  generateValidationReport() {
    return {
      summary: {
        totalErrors: this.errors.length,
        totalWarnings: this.warnings.length,
        validationPassed: this.errors.length === 0
      },
      errors: this.errors,
      warnings: this.warnings,
      recommendations: this.generateRecommendations(),
      timestamp: new Date()
    };
  }

  generateRecommendations() {
    const recommendations = [];

    if (this.errors.length > 0) {
      recommendations.push('Fix all validation errors before running comprehensive tests');
    }

    if (this.warnings.length > 5) {
      recommendations.push('Consider addressing validation warnings to improve test reliability');
    }

    recommendations.push('Run framework validation regularly to catch issues early');
    recommendations.push('Update test definitions when adding new features');

    return recommendations;
  }
}
