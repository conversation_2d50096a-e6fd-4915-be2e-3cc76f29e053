{"name": "comprehensive-test-framework", "version": "1.0.0", "description": "Comprehensive testing framework for investor app platform", "type": "module", "scripts": {"test:comprehensive": "node scripts/run-all-tests.js", "test:backend-api": "node scripts/run-all-tests.js --backend-only", "test:frontend-components": "node scripts/run-all-tests.js --frontend-only", "test:user-flows": "node scripts/run-all-tests.js --e2e-only", "test:integration": "node scripts/run-all-tests.js --integration-only", "test:validate-framework": "node scripts/validate-framework.js", "test:report": "node scripts/generate-reports.js", "test:auto-fix": "node scripts/auto-fix.js", "test:performance": "node scripts/run-all-tests.js --performance-only", "test:security": "node scripts/run-all-tests.js --security-only", "test:quick": "node scripts/run-all-tests.js --quick", "test:smoke": "node scripts/run-all-tests.js --smoke", "test:regression": "node scripts/run-all-tests.js --regression", "setup": "node scripts/setup-framework.js", "clean": "node scripts/clean-test-data.js", "dev": "nodemon scripts/run-all-tests.js --watch", "ci": "node scripts/run-all-tests.js --ci"}, "dependencies": {"axios": "^1.6.0", "chai": "^4.3.10", "mocha": "^10.2.0", "puppeteer": "^21.5.0", "playwright": "^1.40.0", "supertest": "^6.3.3", "jest": "^29.7.0", "cypress": "^13.6.0", "vitest": "^1.0.0", "@vue/test-utils": "^2.4.0", "jsdom": "^23.0.0", "cheerio": "^1.0.0-rc.12", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.1", "fs-extra": "^11.1.1", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.12", "yargs": "^17.7.2", "dotenv": "^16.3.1", "jsonschema": "^1.4.1", "html-reporter": "^1.0.0", "mochawesome": "^7.1.3", "allure-mocha": "^2.10.0"}, "devDependencies": {"eslint": "^8.54.0", "prettier": "^3.1.0", "nodemon": "^3.0.1"}, "keywords": ["testing", "automation", "vue", "express", "e2e", "integration", "api"], "author": "Investor App Team", "license": "MIT"}