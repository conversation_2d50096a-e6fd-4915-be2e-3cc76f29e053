import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import axios from 'axios';
import { testConfig } from '../../config/test-config.js';
import { TestDataGenerator } from '../../utils/data-generators.js';
import { TestHelpers } from '../../utils/test-helpers.js';

describe('Authentication API Tests', () => {
  let dataGenerator;
  let helpers;
  let baseURL;

  beforeAll(() => {
    dataGenerator = new TestDataGenerator();
    helpers = new TestHelpers();
    baseURL = testConfig.api.baseUrl;
  });

  afterAll(async () => {
    await helpers.cleanupTestData();
  });

  describe('POST /auth/register', () => {
    it('should register a new client user with valid data', async () => {
      const userData = dataGenerator.generateForScenario(
        { category: 'auth', name: 'register' },
        'valid_data'
      );

      const response = await axios.post(`${baseURL}/auth/register`, userData);

      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('token');
      expect(response.data).toHaveProperty('user');
      expect(response.data.user.role).toBe('client');
      expect(response.data.user.email).toBe(userData.email);
    });

    it('should register a new manager user with valid data', async () => {
      const userData = dataGenerator.generateForScenario(
        { category: 'auth', name: 'register' },
        'valid_data'
      );
      userData.role = 'manager';

      const response = await axios.post(`${baseURL}/auth/register`, userData);

      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('token');
      expect(response.data.user.role).toBe('manager');
    });

    it('should reject registration with invalid email', async () => {
      const userData = dataGenerator.generateForScenario(
        { category: 'auth', name: 'register' },
        'invalid_email'
      );

      try {
        await axios.post(`${baseURL}/auth/register`, userData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data).toHaveProperty('error');
      }
    });

    it('should reject registration with weak password', async () => {
      const userData = dataGenerator.generateForScenario(
        { category: 'auth', name: 'register' },
        'weak_password'
      );

      try {
        await axios.post(`${baseURL}/auth/register`, userData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toContain('password');
      }
    });

    it('should reject registration with duplicate email', async () => {
      const userData = dataGenerator.generateForScenario(
        { category: 'auth', name: 'register' },
        'duplicate_email'
      );

      try {
        await axios.post(`${baseURL}/auth/register`, userData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toContain('email');
      }
    });

    it('should reject registration with missing required fields', async () => {
      const incompleteData = {
        email: '<EMAIL>'
        // Missing password, role, contactName, etc.
      };

      try {
        await axios.post(`${baseURL}/auth/register`, incompleteData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
      }
    });
  });

  describe('POST /auth/login', () => {
    let testUser;

    beforeAll(async () => {
      // Create a test user for login tests
      testUser = await helpers.createTestUser('client');
      await axios.post(`${baseURL}/auth/register`, testUser);
    });

    it('should login with valid credentials', async () => {
      const loginData = {
        email: testUser.email,
        password: testUser.password
      };

      const response = await axios.post(`${baseURL}/auth/login`, loginData);

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('token');
      expect(response.data).toHaveProperty('user');
      expect(response.data.user.email).toBe(testUser.email);
    });

    it('should login with remember me option', async () => {
      const loginData = {
        email: testUser.email,
        password: testUser.password,
        rememberMe: true
      };

      const response = await axios.post(`${baseURL}/auth/login`, loginData);

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('token');
      
      // Token should have longer expiration when rememberMe is true
      const tokenPayload = JSON.parse(atob(response.data.token.split('.')[1]));
      const expirationTime = tokenPayload.exp * 1000;
      const currentTime = Date.now();
      const timeDiff = expirationTime - currentTime;
      
      // Should be approximately 24 hours (86400000 ms)
      expect(timeDiff).toBeGreaterThan(23 * 60 * 60 * 1000); // At least 23 hours
    });

    it('should reject login with invalid credentials', async () => {
      const loginData = {
        email: testUser.email,
        password: 'wrongpassword'
      };

      try {
        await axios.post(`${baseURL}/auth/login`, loginData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data).toHaveProperty('error');
      }
    });

    it('should reject login with non-existent email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      try {
        await axios.post(`${baseURL}/auth/login`, loginData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });

    it('should reject login with missing credentials', async () => {
      try {
        await axios.post(`${baseURL}/auth/login`, {});
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
      }
    });
  });

  describe('POST /auth/refresh-token', () => {
    let authToken;

    beforeAll(async () => {
      // Login to get a token
      const testUser = await helpers.createTestUser('client');
      await axios.post(`${baseURL}/auth/register`, testUser);
      
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: testUser.email,
        password: testUser.password
      });
      
      authToken = loginResponse.data.token;
    });

    it('should refresh token with valid token', async () => {
      const response = await axios.post(`${baseURL}/auth/refresh-token`, {}, {
        headers: { Authorization: `Bearer ${authToken}` }
      });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('token');
      expect(response.data.token).not.toBe(authToken); // Should be a new token
    });

    it('should reject refresh with invalid token', async () => {
      try {
        await axios.post(`${baseURL}/auth/refresh-token`, {}, {
          headers: { Authorization: 'Bearer invalid-token' }
        });
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });

    it('should reject refresh without token', async () => {
      try {
        await axios.post(`${baseURL}/auth/refresh-token`, {});
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });
  });

  describe('POST /auth/logout', () => {
    let authToken;

    beforeAll(async () => {
      // Login to get a token
      const testUser = await helpers.createTestUser('client');
      await axios.post(`${baseURL}/auth/register`, testUser);
      
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: testUser.email,
        password: testUser.password
      });
      
      authToken = loginResponse.data.token;
    });

    it('should logout successfully with valid token', async () => {
      const response = await axios.post(`${baseURL}/auth/logout`, {}, {
        headers: { Authorization: `Bearer ${authToken}` }
      });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('message');
    });

    it('should reject logout without token', async () => {
      try {
        await axios.post(`${baseURL}/auth/logout`, {});
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });
  });

  describe('Authentication Flow Integration', () => {
    it('should complete full authentication flow', async () => {
      // 1. Register new user
      const userData = await helpers.createTestUser('client');
      const registerResponse = await axios.post(`${baseURL}/auth/register`, userData);
      
      expect(registerResponse.status).toBe(201);
      const initialToken = registerResponse.data.token;

      // 2. Login with the same user
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: userData.email,
        password: userData.password
      });
      
      expect(loginResponse.status).toBe(200);
      const loginToken = loginResponse.data.token;

      // 3. Refresh token
      const refreshResponse = await axios.post(`${baseURL}/auth/refresh-token`, {}, {
        headers: { Authorization: `Bearer ${loginToken}` }
      });
      
      expect(refreshResponse.status).toBe(200);
      const refreshedToken = refreshResponse.data.token;

      // 4. Logout
      const logoutResponse = await axios.post(`${baseURL}/auth/logout`, {}, {
        headers: { Authorization: `Bearer ${refreshedToken}` }
      });
      
      expect(logoutResponse.status).toBe(200);

      // 5. Verify token is invalidated (should fail on subsequent requests)
      try {
        await axios.get(`${baseURL}/profile`, {
          headers: { Authorization: `Bearer ${refreshedToken}` }
        });
        fail('Should have thrown an error after logout');
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });
  });
});
