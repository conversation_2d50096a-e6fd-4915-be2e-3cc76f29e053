#!/usr/bin/env node

import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs-extra';
import chalk from 'chalk';
import ora from 'ora';
import { testConfig } from '../config/test-config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AutoFixAnalyzer {
  constructor() {
    this.fixes = [];
    this.patterns = new Map();
    this.recommendations = [];
  }

  async run() {
    console.log(chalk.blue.bold('\n🔧 Auto-Fix Analysis\n'));

    try {
      // Load latest test results
      const results = await this.loadLatestResults();
      
      if (!results) {
        console.log(chalk.yellow('No test results found. Run tests first.'));
        return;
      }

      // Analyze failures
      await this.analyzeFailures(results);

      // Generate fix suggestions
      await this.generateFixSuggestions();

      // Display results
      this.displayResults();

      // Generate fix script
      await this.generateFixScript();

    } catch (error) {
      console.error(chalk.red.bold('❌ Auto-fix analysis failed:'), error.message);
      process.exit(1);
    }
  }

  async loadLatestResults() {
    try {
      const resultsPath = path.join(testConfig.reporting.outputDir, 'latest', 'agent-report.json');
      
      if (!await fs.pathExists(resultsPath)) {
        return null;
      }

      return await fs.readJSON(resultsPath);
    } catch (error) {
      throw new Error(`Failed to load test results: ${error.message}`);
    }
  }

  async analyzeFailures(results) {
    const spinner = ora('Analyzing test failures...').start();

    try {
      const failures = results.failures || [];
      
      // Group failures by pattern
      for (const failure of failures) {
        const pattern = this.extractPattern(failure);
        
        if (!this.patterns.has(pattern)) {
          this.patterns.set(pattern, []);
        }
        
        this.patterns.get(pattern).push(failure);
      }

      // Analyze each pattern
      for (const [pattern, patternFailures] of this.patterns) {
        const analysis = this.analyzePattern(pattern, patternFailures);
        if (analysis) {
          this.fixes.push(analysis);
        }
      }

      spinner.succeed(`Analyzed ${failures.length} failures, found ${this.fixes.length} fixable patterns`);
    } catch (error) {
      spinner.fail('Failure analysis failed');
      throw error;
    }
  }

  extractPattern(failure) {
    const error = failure.error_message || '';
    
    // Common error patterns
    if (error.includes('timeout')) return 'timeout';
    if (error.includes('404')) return 'not_found';
    if (error.includes('401') || error.includes('unauthorized')) return 'auth_error';
    if (error.includes('403') || error.includes('forbidden')) return 'permission_error';
    if (error.includes('500') || error.includes('internal server error')) return 'server_error';
    if (error.includes('connection refused') || error.includes('ECONNREFUSED')) return 'connection_error';
    if (error.includes('validation') || error.includes('invalid')) return 'validation_error';
    if (error.includes('duplicate') || error.includes('already exists')) return 'duplicate_error';
    if (error.includes('not found') && !error.includes('404')) return 'resource_not_found';
    if (error.includes('rate limit')) return 'rate_limit';
    
    return 'unknown';
  }

  analyzePattern(pattern, failures) {
    const analysis = {
      pattern,
      count: failures.length,
      severity: this.calculateSeverity(pattern, failures),
      description: this.getPatternDescription(pattern),
      fixes: this.generatePatternFixes(pattern, failures),
      examples: failures.slice(0, 3) // Show first 3 examples
    };

    return analysis;
  }

  calculateSeverity(pattern, failures) {
    const severityMap = {
      server_error: 'critical',
      connection_error: 'critical',
      auth_error: 'high',
      permission_error: 'high',
      timeout: 'medium',
      not_found: 'medium',
      validation_error: 'low',
      duplicate_error: 'low',
      rate_limit: 'medium'
    };

    const baseSeverity = severityMap[pattern] || 'low';
    
    // Increase severity if many tests are affected
    if (failures.length > 10) {
      if (baseSeverity === 'low') return 'medium';
      if (baseSeverity === 'medium') return 'high';
      if (baseSeverity === 'high') return 'critical';
    }

    return baseSeverity;
  }

  getPatternDescription(pattern) {
    const descriptions = {
      timeout: 'Tests are timing out, indicating performance issues or slow responses',
      not_found: 'API endpoints are returning 404 errors, indicating routing or URL issues',
      auth_error: 'Authentication is failing, indicating token or credential issues',
      permission_error: 'Authorization is failing, indicating role or permission issues',
      server_error: 'Server is returning 500 errors, indicating backend application issues',
      connection_error: 'Cannot connect to services, indicating infrastructure issues',
      validation_error: 'Data validation is failing, indicating schema or format issues',
      duplicate_error: 'Duplicate data errors, indicating test data cleanup issues',
      resource_not_found: 'Resources are missing, indicating data setup issues',
      rate_limit: 'Rate limiting is being triggered, indicating too many requests'
    };

    return descriptions[pattern] || 'Unknown error pattern detected';
  }

  generatePatternFixes(pattern, failures) {
    const fixes = [];

    switch (pattern) {
      case 'timeout':
        fixes.push({
          type: 'config',
          description: 'Increase timeout values in test configuration',
          action: 'update_config',
          details: {
            file: 'config/test-config.js',
            property: 'api.timeout',
            suggestedValue: Math.max(testConfig.api.timeout * 2, 60000)
          }
        });
        fixes.push({
          type: 'performance',
          description: 'Investigate backend performance issues',
          action: 'manual_investigation',
          details: {
            suggestion: 'Check server logs and database query performance'
          }
        });
        break;

      case 'not_found':
        fixes.push({
          type: 'endpoint',
          description: 'Verify API endpoint URLs and routing',
          action: 'validate_endpoints',
          details: {
            suggestion: 'Run endpoint validation to check for mismatched URLs'
          }
        });
        break;

      case 'auth_error':
        fixes.push({
          type: 'auth',
          description: 'Update test user credentials',
          action: 'refresh_test_users',
          details: {
            suggestion: 'Recreate test users or refresh authentication tokens'
          }
        });
        break;

      case 'connection_error':
        fixes.push({
          type: 'infrastructure',
          description: 'Check service availability',
          action: 'health_check',
          details: {
            suggestion: 'Verify backend and database services are running'
          }
        });
        break;

      case 'validation_error':
        fixes.push({
          type: 'data',
          description: 'Update test data to match current schema',
          action: 'update_test_data',
          details: {
            suggestion: 'Review and update test data generators'
          }
        });
        break;

      case 'duplicate_error':
        fixes.push({
          type: 'cleanup',
          description: 'Improve test data cleanup',
          action: 'enhance_cleanup',
          details: {
            suggestion: 'Ensure unique test data generation and proper cleanup'
          }
        });
        break;
    }

    return fixes;
  }

  async generateFixSuggestions() {
    const spinner = ora('Generating fix suggestions...').start();

    try {
      // Sort fixes by severity and count
      this.fixes.sort((a, b) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
        if (severityDiff !== 0) return severityDiff;
        return b.count - a.count;
      });

      // Generate overall recommendations
      this.generateRecommendations();

      spinner.succeed('Fix suggestions generated');
    } catch (error) {
      spinner.fail('Failed to generate fix suggestions');
      throw error;
    }
  }

  generateRecommendations() {
    const totalFailures = this.fixes.reduce((sum, fix) => sum + fix.count, 0);
    
    if (totalFailures === 0) {
      this.recommendations.push('All tests are passing! No fixes needed.');
      return;
    }

    // High-level recommendations based on patterns
    const criticalFixes = this.fixes.filter(f => f.severity === 'critical');
    const highFixes = this.fixes.filter(f => f.severity === 'high');

    if (criticalFixes.length > 0) {
      this.recommendations.push('🚨 Critical issues detected - address immediately before deployment');
    }

    if (highFixes.length > 0) {
      this.recommendations.push('⚠️ High priority issues found - should be fixed soon');
    }

    // Specific recommendations
    const patterns = this.fixes.map(f => f.pattern);
    
    if (patterns.includes('timeout')) {
      this.recommendations.push('Consider performance optimization or infrastructure scaling');
    }

    if (patterns.includes('auth_error')) {
      this.recommendations.push('Review authentication implementation and test setup');
    }

    if (patterns.includes('connection_error')) {
      this.recommendations.push('Check service dependencies and network configuration');
    }

    if (this.fixes.length > 5) {
      this.recommendations.push('Multiple failure patterns detected - consider systematic review');
    }
  }

  displayResults() {
    console.log(chalk.blue.bold('📊 Auto-Fix Analysis Results\n'));

    if (this.fixes.length === 0) {
      console.log(chalk.green('✅ No fixable issues found!'));
      return;
    }

    // Display fixes by severity
    for (const fix of this.fixes) {
      const severityColor = {
        critical: chalk.red.bold,
        high: chalk.red,
        medium: chalk.yellow,
        low: chalk.blue
      }[fix.severity];

      console.log(severityColor(`${fix.severity.toUpperCase()}: ${fix.pattern} (${fix.count} occurrences)`));
      console.log(chalk.gray(`  ${fix.description}`));
      
      for (const fixAction of fix.fixes) {
        console.log(chalk.cyan(`  💡 ${fixAction.description}`));
        if (fixAction.details.suggestion) {
          console.log(chalk.gray(`     ${fixAction.details.suggestion}`));
        }
      }
      console.log();
    }

    // Display recommendations
    if (this.recommendations.length > 0) {
      console.log(chalk.blue.bold('💡 Recommendations:\n'));
      for (const rec of this.recommendations) {
        console.log(chalk.blue(`  ${rec}`));
      }
      console.log();
    }
  }

  async generateFixScript() {
    const spinner = ora('Generating fix script...').start();

    try {
      const scriptContent = this.generateFixScriptContent();
      const scriptPath = path.join(testConfig.reporting.outputDir, 'latest', 'auto-fix-script.sh');
      
      await fs.writeFile(scriptPath, scriptContent);
      await fs.chmod(scriptPath, '755'); // Make executable

      spinner.succeed(`Fix script generated: ${scriptPath}`);
    } catch (error) {
      spinner.fail('Failed to generate fix script');
      console.warn('Fix script generation error:', error.message);
    }
  }

  generateFixScriptContent() {
    let script = `#!/bin/bash
# Auto-generated fix script
# Generated on ${new Date().toISOString()}

echo "🔧 Applying automatic fixes..."

`;

    for (const fix of this.fixes) {
      script += `# Fix for ${fix.pattern} (${fix.count} occurrences)\n`;
      script += `echo "Fixing ${fix.pattern}..."\n`;

      for (const fixAction of fix.fixes) {
        switch (fixAction.action) {
          case 'update_config':
            script += `# Update configuration\n`;
            script += `echo "Updating ${fixAction.details.file}..."\n`;
            break;
          case 'refresh_test_users':
            script += `# Refresh test users\n`;
            script += `npm run clean\n`;
            break;
          case 'health_check':
            script += `# Health check\n`;
            script += `curl -f ${testConfig.api.baseUrl}/health || echo "Backend not available"\n`;
            break;
        }
      }
      script += '\n';
    }

    script += `echo "✅ Auto-fixes applied. Please review and test."
echo "💡 Some issues may require manual intervention."
`;

    return script;
  }
}

// Run auto-fix analysis if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new AutoFixAnalyzer();
  analyzer.run().catch(error => {
    console.error(chalk.red.bold('Auto-fix analysis failed:'), error);
    process.exit(1);
  });
}
