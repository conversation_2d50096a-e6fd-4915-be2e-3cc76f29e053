#!/usr/bin/env node

import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs-extra';
import chalk from 'chalk';
import ora from 'ora';
import { testConfig, validateConfig } from '../config/test-config.js';
import { TestRunner } from '../utils/test-runner.js';
import { ReportGenerator } from '../utils/reporters.js';
import { TestValidator } from '../utils/validators.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ComprehensiveTestRunner {
  constructor() {
    this.testRunner = new TestRunner();
    this.reportGenerator = new ReportGenerator();
    this.validator = new TestValidator();
    this.results = {
      startTime: new Date(),
      endTime: null,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      categories: {},
      errors: [],
      warnings: []
    };
  }

  async run() {
    console.log(chalk.blue.bold('\n🚀 Starting Comprehensive Test Suite\n'));

    try {
      // Validate configuration
      await this.validateSetup();

      // Run framework self-validation
      await this.runFrameworkValidation();

      // Run all test categories
      await this.runBackendTests();
      await this.runFrontendTests();
      await this.runIntegrationTests();
      await this.runE2ETests();
      await this.runPerformanceTests();
      await this.runSecurityTests();

      // Generate comprehensive report
      await this.generateReports();

      // Display summary
      this.displaySummary();

    } catch (error) {
      console.error(chalk.red.bold('❌ Test suite failed:'), error.message);
      process.exit(1);
    }
  }

  async validateSetup() {
    const spinner = ora('Validating test setup...').start();
    
    try {
      // Validate configuration
      validateConfig();
      
      // Check if backend is running
      await this.testRunner.checkBackendHealth();
      
      // Check if frontend is accessible
      await this.testRunner.checkFrontendHealth();
      
      // Verify test database connection
      await this.testRunner.checkDatabaseConnection();
      
      spinner.succeed('Test setup validation completed');
    } catch (error) {
      spinner.fail('Test setup validation failed');
      throw error;
    }
  }

  async runFrameworkValidation() {
    const spinner = ora('Running framework self-validation...').start();
    
    try {
      const validationResults = await this.validator.validateFramework();
      
      if (validationResults.warnings.length > 0) {
        this.results.warnings.push(...validationResults.warnings);
        spinner.warn(`Framework validation completed with ${validationResults.warnings.length} warnings`);
      } else {
        spinner.succeed('Framework validation completed');
      }
      
      this.results.categories.frameworkValidation = validationResults;
    } catch (error) {
      spinner.fail('Framework validation failed');
      this.results.errors.push({
        category: 'framework_validation',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  async runBackendTests() {
    const spinner = ora('Running backend API tests...').start();
    
    try {
      const backendResults = await this.testRunner.runBackendTests();
      this.updateResults('backend', backendResults);
      
      spinner.succeed(`Backend tests completed: ${backendResults.passed}/${backendResults.total} passed`);
    } catch (error) {
      spinner.fail('Backend tests failed');
      this.results.errors.push({
        category: 'backend',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  async runFrontendTests() {
    const spinner = ora('Running frontend component tests...').start();
    
    try {
      const frontendResults = await this.testRunner.runFrontendTests();
      this.updateResults('frontend', frontendResults);
      
      spinner.succeed(`Frontend tests completed: ${frontendResults.passed}/${frontendResults.total} passed`);
    } catch (error) {
      spinner.fail('Frontend tests failed');
      this.results.errors.push({
        category: 'frontend',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  async runIntegrationTests() {
    const spinner = ora('Running integration tests...').start();
    
    try {
      const integrationResults = await this.testRunner.runIntegrationTests();
      this.updateResults('integration', integrationResults);
      
      spinner.succeed(`Integration tests completed: ${integrationResults.passed}/${integrationResults.total} passed`);
    } catch (error) {
      spinner.fail('Integration tests failed');
      this.results.errors.push({
        category: 'integration',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  async runE2ETests() {
    const spinner = ora('Running end-to-end user flow tests...').start();
    
    try {
      const e2eResults = await this.testRunner.runE2ETests();
      this.updateResults('e2e', e2eResults);
      
      spinner.succeed(`E2E tests completed: ${e2eResults.passed}/${e2eResults.total} passed`);
    } catch (error) {
      spinner.fail('E2E tests failed');
      this.results.errors.push({
        category: 'e2e',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  async runPerformanceTests() {
    if (!testConfig.features.testPerformance) {
      console.log(chalk.yellow('⏭️  Performance tests skipped (disabled in config)'));
      return;
    }

    const spinner = ora('Running performance tests...').start();
    
    try {
      const performanceResults = await this.testRunner.runPerformanceTests();
      this.updateResults('performance', performanceResults);
      
      spinner.succeed(`Performance tests completed: ${performanceResults.passed}/${performanceResults.total} passed`);
    } catch (error) {
      spinner.fail('Performance tests failed');
      this.results.errors.push({
        category: 'performance',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  async runSecurityTests() {
    if (!testConfig.security.enableSecurityTests) {
      console.log(chalk.yellow('⏭️  Security tests skipped (disabled in config)'));
      return;
    }

    const spinner = ora('Running security tests...').start();
    
    try {
      const securityResults = await this.testRunner.runSecurityTests();
      this.updateResults('security', securityResults);
      
      spinner.succeed(`Security tests completed: ${securityResults.passed}/${securityResults.total} passed`);
    } catch (error) {
      spinner.fail('Security tests failed');
      this.results.errors.push({
        category: 'security',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  updateResults(category, categoryResults) {
    this.results.categories[category] = categoryResults;
    this.results.totalTests += categoryResults.total;
    this.results.passedTests += categoryResults.passed;
    this.results.failedTests += categoryResults.failed;
    this.results.skippedTests += categoryResults.skipped || 0;
  }

  async generateReports() {
    const spinner = ora('Generating test reports...').start();
    
    try {
      this.results.endTime = new Date();
      this.results.duration = this.results.endTime - this.results.startTime;
      
      await this.reportGenerator.generateComprehensiveReport(this.results);
      
      spinner.succeed('Test reports generated');
    } catch (error) {
      spinner.fail('Report generation failed');
      console.error(chalk.red('Report generation error:'), error.message);
    }
  }

  displaySummary() {
    console.log(chalk.blue.bold('\n📊 Test Suite Summary\n'));
    
    const duration = Math.round(this.results.duration / 1000);
    const successRate = Math.round((this.results.passedTests / this.results.totalTests) * 100);
    
    console.log(`⏱️  Duration: ${duration}s`);
    console.log(`📈 Success Rate: ${successRate}%`);
    console.log(`✅ Passed: ${chalk.green(this.results.passedTests)}`);
    console.log(`❌ Failed: ${chalk.red(this.results.failedTests)}`);
    console.log(`⏭️  Skipped: ${chalk.yellow(this.results.skippedTests)}`);
    console.log(`📊 Total: ${this.results.totalTests}`);
    
    if (this.results.warnings.length > 0) {
      console.log(`⚠️  Warnings: ${chalk.yellow(this.results.warnings.length)}`);
    }
    
    if (this.results.errors.length > 0) {
      console.log(`🚨 Errors: ${chalk.red(this.results.errors.length)}`);
    }
    
    console.log(`\n📁 Reports available in: ${testConfig.reporting.outputDir}/latest/`);
    
    // Exit with appropriate code
    const exitCode = this.results.failedTests > 0 ? 1 : 0;
    process.exit(exitCode);
  }
}

// Run the comprehensive test suite
if (import.meta.url === `file://${process.argv[1]}`) {
  const runner = new ComprehensiveTestRunner();
  runner.run().catch(error => {
    console.error(chalk.red.bold('Fatal error:'), error);
    process.exit(1);
  });
}
