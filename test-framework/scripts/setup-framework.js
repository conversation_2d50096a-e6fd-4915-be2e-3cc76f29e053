#!/usr/bin/env node

import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs-extra';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import { testConfig } from '../config/test-config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FrameworkSetup {
  constructor() {
    this.config = { ...testConfig };
  }

  async run() {
    console.log(chalk.blue.bold('\n🚀 Setting up Comprehensive Test Framework\n'));

    try {
      // Welcome and overview
      await this.showWelcome();

      // Gather configuration
      await this.gatherConfiguration();

      // Create directory structure
      await this.createDirectoryStructure();

      // Install dependencies
      await this.installDependencies();

      // Create configuration files
      await this.createConfigurationFiles();

      // Setup test data
      await this.setupTestData();

      // Validate setup
      await this.validateSetup();

      // Show completion message
      this.showCompletion();

    } catch (error) {
      console.error(chalk.red.bold('❌ Setup failed:'), error.message);
      process.exit(1);
    }
  }

  async showWelcome() {
    console.log(chalk.cyan(`
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║           Comprehensive Test Framework Setup                 ║
║                                                              ║
║  This setup will configure a complete testing framework     ║
║  for your investor application platform.                    ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    `));

    const { proceed } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'proceed',
        message: 'Do you want to proceed with the setup?',
        default: true
      }
    ]);

    if (!proceed) {
      console.log(chalk.yellow('Setup cancelled.'));
      process.exit(0);
    }
  }

  async gatherConfiguration() {
    console.log(chalk.blue.bold('\n📋 Configuration Setup\n'));

    const questions = [
      {
        type: 'input',
        name: 'apiUrl',
        message: 'Backend API URL:',
        default: this.config.api.baseUrl,
        validate: (input) => {
          try {
            new URL(input);
            return true;
          } catch {
            return 'Please enter a valid URL';
          }
        }
      },
      {
        type: 'input',
        name: 'frontendUrl',
        message: 'Frontend URL:',
        default: this.config.frontend.baseUrl,
        validate: (input) => {
          try {
            new URL(input);
            return true;
          } catch {
            return 'Please enter a valid URL';
          }
        }
      },
      {
        type: 'input',
        name: 'testDatabaseUrl',
        message: 'Test Database URL:',
        default: this.config.database.testUrl || 'postgresql://localhost:5432/investor_app_test'
      },
      {
        type: 'input',
        name: 'outputDir',
        message: 'Test reports output directory:',
        default: this.config.reporting.outputDir
      },
      {
        type: 'checkbox',
        name: 'testTypes',
        message: 'Select test types to enable:',
        choices: [
          { name: 'Backend API Tests', value: 'backend', checked: true },
          { name: 'Frontend Component Tests', value: 'frontend', checked: true },
          { name: 'End-to-End Tests', value: 'e2e', checked: true },
          { name: 'Integration Tests', value: 'integration', checked: true },
          { name: 'Performance Tests', value: 'performance', checked: false },
          { name: 'Security Tests', value: 'security', checked: false }
        ]
      },
      {
        type: 'list',
        name: 'browser',
        message: 'Default browser for E2E tests:',
        choices: ['chromium', 'firefox', 'webkit'],
        default: 'chromium'
      },
      {
        type: 'confirm',
        name: 'headless',
        message: 'Run E2E tests in headless mode?',
        default: true
      },
      {
        type: 'number',
        name: 'timeout',
        message: 'Default test timeout (seconds):',
        default: 30,
        validate: (input) => input > 0 || 'Timeout must be greater than 0'
      }
    ];

    const answers = await inquirer.prompt(questions);

    // Update configuration
    this.config.api.baseUrl = answers.apiUrl;
    this.config.frontend.baseUrl = answers.frontendUrl;
    this.config.database.testUrl = answers.testDatabaseUrl;
    this.config.reporting.outputDir = answers.outputDir;
    this.config.browsers.defaultBrowser = answers.browser;
    this.config.browsers.headless = answers.headless;
    this.config.execution.timeout = answers.timeout * 1000;

    // Enable/disable test types
    this.config.features.testBackend = answers.testTypes.includes('backend');
    this.config.features.testFrontend = answers.testTypes.includes('frontend');
    this.config.features.testE2E = answers.testTypes.includes('e2e');
    this.config.features.testIntegration = answers.testTypes.includes('integration');
    this.config.features.testPerformance = answers.testTypes.includes('performance');
    this.config.security.enableSecurityTests = answers.testTypes.includes('security');
  }

  async createDirectoryStructure() {
    const spinner = ora('Creating directory structure...').start();

    try {
      const directories = [
        'backend/api-tests',
        'backend/service-tests',
        'backend/integration-tests',
        'backend/runners',
        'frontend/component-tests',
        'frontend/store-tests',
        'frontend/integration-tests',
        'frontend/runners',
        'e2e/user-flows',
        'e2e/cross-browser',
        'e2e/performance',
        'utils',
        'reports/latest',
        'reports/history',
        'reports/templates',
        'reports/screenshots',
        'scripts',
        'config'
      ];

      for (const dir of directories) {
        await fs.ensureDir(path.join(__dirname, '..', dir));
      }

      spinner.succeed('Directory structure created');
    } catch (error) {
      spinner.fail('Failed to create directory structure');
      throw error;
    }
  }

  async installDependencies() {
    const spinner = ora('Installing dependencies...').start();

    try {
      // In a real implementation, this would run npm install
      // For now, we'll just simulate the process
      await this.delay(2000);
      
      spinner.succeed('Dependencies installed');
    } catch (error) {
      spinner.fail('Failed to install dependencies');
      throw error;
    }
  }

  async createConfigurationFiles() {
    const spinner = ora('Creating configuration files...').start();

    try {
      // Create test configuration file
      const configPath = path.join(__dirname, '..', 'config', 'test-config.local.js');
      const configContent = this.generateConfigFile();
      await fs.writeFile(configPath, configContent);

      // Create environment file
      const envPath = path.join(__dirname, '..', '.env.test');
      const envContent = this.generateEnvFile();
      await fs.writeFile(envPath, envContent);

      // Create package.json scripts
      await this.updatePackageScripts();

      spinner.succeed('Configuration files created');
    } catch (error) {
      spinner.fail('Failed to create configuration files');
      throw error;
    }
  }

  generateConfigFile() {
    return `// Auto-generated test configuration
export const testConfig = ${JSON.stringify(this.config, null, 2)};

export default testConfig;
`;
  }

  generateEnvFile() {
    return `# Test Environment Configuration
NODE_ENV=test
VITE_API_URL=${this.config.api.baseUrl}
FRONTEND_URL=${this.config.frontend.baseUrl}
DATABASE_URL_TEST=${this.config.database.testUrl}

# Test Data
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!

# External Services (for mocking)
CLOUDINARY_CLOUD_NAME=test-cloud
CLOUDINARY_API_KEY=test-key
CLOUDINARY_API_SECRET=test-secret
`;
  }

  async updatePackageScripts() {
    const packagePath = path.join(__dirname, '..', 'package.json');
    const packageJson = await fs.readJSON(packagePath);

    // Add additional scripts based on enabled features
    const additionalScripts = {};

    if (this.config.features.testBackend) {
      additionalScripts['test:backend-only'] = 'jest backend/';
    }

    if (this.config.features.testFrontend) {
      additionalScripts['test:frontend-only'] = 'vitest frontend/';
    }

    if (this.config.features.testE2E) {
      additionalScripts['test:e2e-only'] = 'playwright test e2e/';
    }

    packageJson.scripts = { ...packageJson.scripts, ...additionalScripts };
    await fs.writeJSON(packagePath, packageJson, { spaces: 2 });
  }

  async setupTestData() {
    const spinner = ora('Setting up test data...').start();

    try {
      // Create test data directory
      const testDataDir = path.join(__dirname, '..', 'test-data');
      await fs.ensureDir(testDataDir);

      // Create sample test files
      const sampleFiles = {
        'sample-selfie.jpg': this.generateSampleImageData(),
        'sample-passport.jpg': this.generateSampleImageData(),
        'sample-document.pdf': this.generateSampleDocumentData()
      };

      for (const [filename, content] of Object.entries(sampleFiles)) {
        await fs.writeFile(path.join(testDataDir, filename), content);
      }

      spinner.succeed('Test data setup completed');
    } catch (error) {
      spinner.fail('Failed to setup test data');
      throw error;
    }
  }

  generateSampleImageData() {
    // Generate a minimal valid JPEG header
    return Buffer.from([
      0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
      0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xD9
    ]);
  }

  generateSampleDocumentData() {
    // Generate a minimal valid PDF header
    return Buffer.from('%PDF-1.4\n%âãÏÓ\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n%%EOF');
  }

  async validateSetup() {
    const spinner = ora('Validating setup...').start();

    try {
      // Check if all required files exist
      const requiredFiles = [
        'config/test-config.js',
        'utils/test-runner.js',
        'utils/data-generators.js',
        'utils/validators.js',
        'utils/reporters.js',
        'scripts/run-all-tests.js'
      ];

      for (const file of requiredFiles) {
        const filePath = path.join(__dirname, '..', file);
        if (!await fs.pathExists(filePath)) {
          throw new Error(`Required file missing: ${file}`);
        }
      }

      // Check if output directory is writable
      const testFile = path.join(this.config.reporting.outputDir, 'test-write.tmp');
      await fs.writeFile(testFile, 'test');
      await fs.remove(testFile);

      spinner.succeed('Setup validation passed');
    } catch (error) {
      spinner.fail('Setup validation failed');
      throw error;
    }
  }

  showCompletion() {
    console.log(chalk.green.bold('\n✅ Test Framework Setup Complete!\n'));

    console.log(chalk.cyan('📁 Framework Structure:'));
    console.log(`   ${this.config.reporting.outputDir}/`);
    console.log('   ├── backend/          # Backend API tests');
    console.log('   ├── frontend/         # Frontend component tests');
    console.log('   ├── e2e/              # End-to-end user flow tests');
    console.log('   ├── utils/            # Test utilities and helpers');
    console.log('   ├── config/           # Test configuration');
    console.log('   └── reports/          # Test reports and results\n');

    console.log(chalk.cyan('🚀 Quick Start:'));
    console.log(chalk.white('   npm run test:validate-framework  # Validate framework setup'));
    console.log(chalk.white('   npm run test:comprehensive       # Run all tests'));
    console.log(chalk.white('   npm run test:backend-api         # Run backend tests only'));
    console.log(chalk.white('   npm run test:frontend-components # Run frontend tests only'));
    console.log(chalk.white('   npm run test:user-flows          # Run E2E tests only\n'));

    console.log(chalk.cyan('📊 Reports:'));
    console.log(chalk.white(`   Reports will be generated in: ${this.config.reporting.outputDir}/latest/\n`));

    console.log(chalk.yellow('💡 Next Steps:'));
    console.log(chalk.white('   1. Review and customize test configuration'));
    console.log(chalk.white('   2. Add test-specific data-testid attributes to your components'));
    console.log(chalk.white('   3. Run framework validation to ensure everything is working'));
    console.log(chalk.white('   4. Execute your first comprehensive test run\n'));

    console.log(chalk.green('Happy Testing! 🎉\n'));
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const setup = new FrameworkSetup();
  setup.run().catch(error => {
    console.error(chalk.red.bold('Setup failed:'), error);
    process.exit(1);
  });
}
