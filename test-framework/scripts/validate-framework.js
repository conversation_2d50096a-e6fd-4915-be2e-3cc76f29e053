#!/usr/bin/env node

import { fileURLToPath } from 'url';
import path from 'path';
import chalk from 'chalk';
import ora from 'ora';
import { TestValidator } from '../utils/validators.js';
import { testConfig } from '../config/test-config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FrameworkValidator {
  constructor() {
    this.validator = new TestValidator();
  }

  async run() {
    console.log(chalk.blue.bold('\n🔍 Validating Test Framework\n'));

    try {
      // Run comprehensive validation
      const results = await this.validator.validateFramework();

      // Display results
      this.displayResults(results);

      // Generate validation report
      await this.generateReport(results);

      // Exit with appropriate code
      const exitCode = results.errors.length > 0 ? 1 : 0;
      process.exit(exitCode);

    } catch (error) {
      console.error(chalk.red.bold('❌ Framework validation failed:'), error.message);
      process.exit(1);
    }
  }

  displayResults(results) {
    console.log(chalk.blue.bold('📊 Validation Results\n'));

    // Overall status
    if (results.passed) {
      console.log(chalk.green('✅ Framework validation passed'));
    } else {
      console.log(chalk.red('❌ Framework validation failed'));
    }

    console.log(`⚠️  Warnings: ${chalk.yellow(results.warnings.length)}`);
    console.log(`🚨 Errors: ${chalk.red(results.errors.length)}\n`);

    // Display warnings
    if (results.warnings.length > 0) {
      console.log(chalk.yellow.bold('⚠️  Warnings:\n'));
      results.warnings.forEach((warning, index) => {
        console.log(chalk.yellow(`${index + 1}. ${warning}`));
      });
      console.log();
    }

    // Display errors
    if (results.errors.length > 0) {
      console.log(chalk.red.bold('🚨 Errors:\n'));
      results.errors.forEach((error, index) => {
        console.log(chalk.red(`${index + 1}. ${error}`));
      });
      console.log();
    }

    // Display recommendations
    const report = this.validator.generateValidationReport();
    if (report.recommendations.length > 0) {
      console.log(chalk.blue.bold('💡 Recommendations:\n'));
      report.recommendations.forEach((rec, index) => {
        console.log(chalk.blue(`${index + 1}. ${rec}`));
      });
      console.log();
    }
  }

  async generateReport(results) {
    const spinner = ora('Generating validation report...').start();

    try {
      const report = this.validator.generateValidationReport();
      
      // Save report to file
      const fs = await import('fs-extra');
      const reportPath = path.join(testConfig.reporting.outputDir, 'latest', 'framework-validation.json');
      await fs.ensureDir(path.dirname(reportPath));
      await fs.writeJSON(reportPath, report, { spaces: 2 });

      spinner.succeed(`Validation report saved to: ${reportPath}`);
    } catch (error) {
      spinner.fail('Failed to generate validation report');
      console.error(chalk.red('Report generation error:'), error.message);
    }
  }
}

// Self-validation checks
async function runSelfValidation() {
  const checks = [
    {
      name: 'Configuration file exists',
      check: async () => {
        const fs = await import('fs-extra');
        return fs.pathExists(path.join(__dirname, '../config/test-config.js'));
      }
    },
    {
      name: 'Endpoints configuration exists',
      check: async () => {
        const fs = await import('fs-extra');
        return fs.pathExists(path.join(__dirname, '../config/endpoints.js'));
      }
    },
    {
      name: 'User flows configuration exists',
      check: async () => {
        const fs = await import('fs-extra');
        return fs.pathExists(path.join(__dirname, '../config/user-flows.js'));
      }
    },
    {
      name: 'Test utilities exist',
      check: async () => {
        const fs = await import('fs-extra');
        const utilsDir = path.join(__dirname, '../utils');
        const requiredFiles = ['test-runner.js', 'data-generators.js', 'validators.js', 'reporters.js', 'test-helpers.js'];
        
        for (const file of requiredFiles) {
          if (!await fs.pathExists(path.join(utilsDir, file))) {
            return false;
          }
        }
        return true;
      }
    },
    {
      name: 'Output directory is writable',
      check: async () => {
        const fs = await import('fs-extra');
        try {
          await fs.ensureDir(testConfig.reporting.outputDir);
          const testFile = path.join(testConfig.reporting.outputDir, 'test-write.tmp');
          await fs.writeFile(testFile, 'test');
          await fs.remove(testFile);
          return true;
        } catch {
          return false;
        }
      }
    }
  ];

  console.log(chalk.blue.bold('🔧 Running Framework Self-Validation\n'));

  let allPassed = true;
  for (const check of checks) {
    const spinner = ora(check.name).start();
    try {
      const passed = await check.check();
      if (passed) {
        spinner.succeed();
      } else {
        spinner.fail();
        allPassed = false;
      }
    } catch (error) {
      spinner.fail(`${check.name} - ${error.message}`);
      allPassed = false;
    }
  }

  if (!allPassed) {
    console.log(chalk.red.bold('\n❌ Self-validation failed. Please fix the issues above before proceeding.\n'));
    process.exit(1);
  }

  console.log(chalk.green.bold('\n✅ Framework self-validation passed\n'));
}

// Check for codebase structure
async function validateCodebaseStructure() {
  const spinner = ora('Validating codebase structure...').start();

  try {
    const fs = await import('fs-extra');
    const requiredPaths = [
      '../../investor-app-backend',
      '../../investor-app-frontend',
      '../../investor-app-backend/src/routes',
      '../../investor-app-frontend/src/components',
      '../../investor-app-frontend/src/stores',
      '../../investor-app-frontend/src/views'
    ];

    for (const relativePath of requiredPaths) {
      const fullPath = path.resolve(__dirname, relativePath);
      if (!await fs.pathExists(fullPath)) {
        throw new Error(`Required path not found: ${relativePath}`);
      }
    }

    spinner.succeed('Codebase structure validation passed');
  } catch (error) {
    spinner.fail('Codebase structure validation failed');
    throw error;
  }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    try {
      // Run self-validation first
      await runSelfValidation();
      
      // Validate codebase structure
      await validateCodebaseStructure();
      
      // Run main framework validation
      const validator = new FrameworkValidator();
      await validator.run();
      
    } catch (error) {
      console.error(chalk.red.bold('Fatal validation error:'), error.message);
      process.exit(1);
    }
  })();
}
