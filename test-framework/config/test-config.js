import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

export const testConfig = {
  // Environment settings
  environment: process.env.NODE_ENV || 'test',
  
  // API configuration
  api: {
    baseUrl: process.env.VITE_API_URL || 'http://localhost:3000',
    timeout: 30000,
    retries: 3,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  },

  // Frontend configuration
  frontend: {
    baseUrl: process.env.FRONTEND_URL || 'http://localhost:4173',
    timeout: 30000,
    viewport: {
      width: 1280,
      height: 720
    }
  },

  // Database configuration
  database: {
    testUrl: process.env.DATABASE_URL_TEST,
    cleanupAfterTests: true,
    seedData: true
  },

  // Test data configuration
  testData: {
    users: {
      manager: {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: 'manager',
        contactName: 'Test Manager',
        taxId: '*********',
        phone: '*********0'
      },
      client: {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: 'client',
        contactName: 'Test Client',
        taxId: '*********',
        phone: '0*********'
      },
      admin: {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: 'admin',
        contactName: 'Test Admin',
        taxId: '*********',
        phone: '*********7'
      }
    },
    assets: {
      sample: {
        name: 'Test Asset',
        type: 'real_estate',
        value: 100000,
        ownership_percentage: 50,
        metadata: {
          description: 'Test asset for automated testing',
          location: 'Test City'
        }
      }
    },
    messages: {
      sample: {
        subject: 'Test Message',
        body: 'This is a test message for automated testing purposes.'
      }
    }
  },

  // Test execution configuration
  execution: {
    parallel: true,
    maxConcurrency: 4,
    timeout: 60000,
    retries: 2,
    bail: false,
    verbose: true
  },

  // Reporting configuration
  reporting: {
    outputDir: './reports',
    formats: ['json', 'html', 'junit'],
    includeScreenshots: true,
    includeLogs: true,
    generateTrends: true
  },

  // Browser configuration for E2E tests
  browsers: {
    headless: process.env.CI === 'true',
    browsers: ['chromium', 'firefox', 'webkit'],
    defaultBrowser: 'chromium'
  },

  // Performance testing configuration
  performance: {
    thresholds: {
      responseTime: 2000,
      loadTime: 5000,
      memoryUsage: 100 * 1024 * 1024 // 100MB
    }
  },

  // Security testing configuration
  security: {
    enableSecurityTests: true,
    checkForVulnerabilities: true,
    validateInputSanitization: true
  },

  // Feature flags for testing
  features: {
    testKycVerification: true,
    testMessaging: true,
    testAssetTokenization: true,
    testClientManagement: true,
    testNotifications: true,
    testSessionManagement: true,
    testDashboardWalkthrough: true,
    testReporting: true,
    testVestingContracts: true
  },

  // External service mocking
  mocking: {
    enableMocks: true,
    mockCloudinary: true,
    mockBlockchain: true,
    mockEmailService: true,
    mockKycService: true
  }
};

// Validation function to ensure configuration is complete
export function validateConfig() {
  const required = [
    'api.baseUrl',
    'frontend.baseUrl',
    'database.testUrl'
  ];

  const missing = [];
  
  for (const path of required) {
    const value = path.split('.').reduce((obj, key) => obj?.[key], testConfig);
    if (!value) {
      missing.push(path);
    }
  }

  if (missing.length > 0) {
    throw new Error(`Missing required configuration: ${missing.join(', ')}`);
  }

  return true;
}

export default testConfig;
