/**
 * API Endpoints Configuration
 * Defines all backend API endpoints for systematic testing
 */

export const endpoints = {
  // Authentication endpoints
  auth: {
    register: {
      method: 'POST',
      path: '/auth/register',
      requiresAuth: false,
      description: 'User registration',
      testScenarios: ['valid_data', 'invalid_email', 'weak_password', 'duplicate_email']
    },
    login: {
      method: 'POST',
      path: '/auth/login',
      requiresAuth: false,
      description: 'User login',
      testScenarios: ['valid_credentials', 'invalid_credentials', 'remember_me']
    },
    refreshToken: {
      method: 'POST',
      path: '/auth/refresh-token',
      requiresAuth: true,
      description: 'Refresh JWT token',
      testScenarios: ['valid_token', 'expired_token', 'invalid_token']
    },
    logout: {
      method: 'POST',
      path: '/auth/logout',
      requiresAuth: true,
      description: 'User logout',
      testScenarios: ['successful_logout']
    }
  },

  // Profile management endpoints
  profile: {
    get: {
      method: 'GET',
      path: '/profile',
      requiresAuth: true,
      description: 'Get user profile',
      testScenarios: ['authenticated_user', 'unauthenticated_user']
    },
    update: {
      method: 'PUT',
      path: '/profile',
      requiresAuth: true,
      description: 'Update user profile',
      testScenarios: ['valid_update', 'invalid_data', 'unauthorized_update']
    },
    uploadImage: {
      method: 'POST',
      path: '/profile/upload-image',
      requiresAuth: true,
      description: 'Upload profile image',
      testScenarios: ['valid_image', 'invalid_format', 'oversized_file']
    }
  },

  // Asset tokenization endpoints
  tokenisation: {
    create: {
      method: 'POST',
      path: '/tokenisation/create',
      requiresAuth: true,
      requiresRole: 'manager',
      description: 'Create tokenized asset',
      testScenarios: ['valid_asset', 'invalid_data', 'unauthorized_user']
    },
    list: {
      method: 'GET',
      path: '/tokenisation',
      requiresAuth: true,
      description: 'List tokenized assets',
      testScenarios: ['manager_view', 'client_view', 'empty_list']
    },
    approve: {
      method: 'PUT',
      path: '/tokenisation/approve/:id',
      requiresAuth: true,
      requiresRole: 'manager',
      description: 'Approve tokenized asset',
      testScenarios: ['valid_approval', 'invalid_id', 'unauthorized_user']
    },
    reject: {
      method: 'PUT',
      path: '/tokenisation/reject/:id',
      requiresAuth: true,
      requiresRole: 'manager',
      description: 'Reject tokenized asset',
      testScenarios: ['valid_rejection', 'invalid_id', 'unauthorized_user']
    }
  },

  // Client management endpoints
  clients: {
    search: {
      method: 'GET',
      path: '/clients/search',
      requiresAuth: true,
      requiresRole: 'manager',
      description: 'Search clients',
      testScenarios: ['valid_search', 'empty_results', 'unauthorized_user']
    },
    link: {
      method: 'POST',
      path: '/clients/link',
      requiresAuth: true,
      description: 'Link client to manager',
      testScenarios: ['valid_link', 'duplicate_link', 'invalid_client']
    },
    list: {
      method: 'GET',
      path: '/clients',
      requiresAuth: true,
      requiresRole: 'manager',
      description: 'List managed clients',
      testScenarios: ['manager_with_clients', 'manager_without_clients']
    }
  },

  // Notifications endpoints
  notifications: {
    list: {
      method: 'GET',
      path: '/notifications',
      requiresAuth: true,
      description: 'Get user notifications',
      testScenarios: ['with_notifications', 'empty_notifications']
    },
    markRead: {
      method: 'PUT',
      path: '/notifications/:id/mark-read',
      requiresAuth: true,
      description: 'Mark notification as read',
      testScenarios: ['valid_notification', 'invalid_id', 'unauthorized_access']
    },
    create: {
      method: 'POST',
      path: '/notifications',
      requiresAuth: true,
      description: 'Create notification',
      testScenarios: ['valid_notification', 'invalid_data']
    }
  },

  // KYC verification endpoints
  kyc: {
    verify: {
      method: 'POST',
      path: '/kyc/verify',
      requiresAuth: true,
      description: 'Submit KYC verification',
      testScenarios: ['valid_documents', 'invalid_documents', 'missing_files']
    },
    status: {
      method: 'GET',
      path: '/kyc/status',
      requiresAuth: true,
      description: 'Get KYC verification status',
      testScenarios: ['verified_user', 'pending_user', 'failed_user']
    }
  },

  // Messaging endpoints
  messages: {
    send: {
      method: 'POST',
      path: '/messages',
      requiresAuth: true,
      description: 'Send message',
      testScenarios: ['valid_message', 'broadcast_message', 'sensitive_content']
    },
    list: {
      method: 'GET',
      path: '/messages',
      requiresAuth: true,
      description: 'Get messages',
      testScenarios: ['manager_messages', 'client_messages', 'empty_inbox']
    },
    markRead: {
      method: 'PUT',
      path: '/messages/:id/read',
      requiresAuth: true,
      description: 'Mark message as read',
      testScenarios: ['valid_message', 'invalid_id']
    }
  },

  // Vesting contracts endpoints
  vesting: {
    create: {
      method: 'POST',
      path: '/vesting/create',
      requiresAuth: true,
      requiresRole: 'manager',
      description: 'Create vesting contract',
      testScenarios: ['valid_contract', 'invalid_data']
    },
    list: {
      method: 'GET',
      path: '/vesting',
      requiresAuth: true,
      description: 'List vesting contracts',
      testScenarios: ['manager_view', 'client_view']
    }
  },

  // Reports endpoints
  reports: {
    generate: {
      method: 'POST',
      path: '/reports/generate',
      requiresAuth: true,
      description: 'Generate report',
      testScenarios: ['valid_report', 'invalid_parameters']
    },
    list: {
      method: 'GET',
      path: '/reports',
      requiresAuth: true,
      description: 'List available reports',
      testScenarios: ['manager_reports', 'client_reports']
    }
  },

  // Activity tracking endpoints
  activity: {
    list: {
      method: 'GET',
      path: '/activity',
      requiresAuth: true,
      description: 'Get user activity',
      testScenarios: ['recent_activity', 'empty_activity']
    },
    log: {
      method: 'POST',
      path: '/activity/log',
      requiresAuth: true,
      description: 'Log user activity',
      testScenarios: ['valid_activity', 'invalid_data']
    }
  },

  // Health check and utility endpoints
  health: {
    check: {
      method: 'GET',
      path: '/health',
      requiresAuth: false,
      description: 'Health check',
      testScenarios: ['healthy_service']
    }
  }
};

// Helper function to get all endpoints as a flat array
export function getAllEndpoints() {
  const allEndpoints = [];
  
  for (const [category, categoryEndpoints] of Object.entries(endpoints)) {
    for (const [name, endpoint] of Object.entries(categoryEndpoints)) {
      allEndpoints.push({
        category,
        name,
        ...endpoint
      });
    }
  }
  
  return allEndpoints;
}

// Helper function to get endpoints by category
export function getEndpointsByCategory(category) {
  return endpoints[category] || {};
}

// Helper function to get endpoints requiring authentication
export function getAuthenticatedEndpoints() {
  return getAllEndpoints().filter(endpoint => endpoint.requiresAuth);
}

// Helper function to get endpoints requiring specific roles
export function getEndpointsByRole(role) {
  return getAllEndpoints().filter(endpoint => endpoint.requiresRole === role);
}

export default endpoints;
