/**
 * User Flow Definitions
 * Defines complete user journeys for end-to-end testing
 */

export const userFlows = {
  // Authentication and onboarding flows
  userOnboarding: {
    name: 'Complete User Onboarding',
    description: 'Full user registration, verification, and initial setup',
    roles: ['client', 'manager'],
    steps: [
      {
        name: 'Visit landing page',
        action: 'navigate',
        target: '/',
        validation: 'page_loads'
      },
      {
        name: 'Navigate to registration',
        action: 'click',
        target: '[data-testid="register-button"]',
        validation: 'registration_form_visible'
      },
      {
        name: 'Fill registration form',
        action: 'form_fill',
        target: '[data-testid="registration-form"]',
        data: 'dynamic_user_data',
        validation: 'form_completed'
      },
      {
        name: 'Submit registration',
        action: 'click',
        target: '[data-testid="submit-registration"]',
        validation: 'registration_success'
      },
      {
        name: 'Verify email confirmation',
        action: 'email_check',
        validation: 'confirmation_email_received'
      },
      {
        name: 'Login with new account',
        action: 'login',
        data: 'registered_user_credentials',
        validation: 'dashboard_accessible'
      },
      {
        name: 'Complete profile setup',
        action: 'profile_setup',
        validation: 'profile_completed'
      }
    ],
    expectedDuration: 120000, // 2 minutes
    cleanup: 'delete_test_user'
  },

  kycVerification: {
    name: 'KYC Verification Process',
    description: 'Complete KYC verification workflow',
    roles: ['client'],
    prerequisites: ['authenticated_user'],
    steps: [
      {
        name: 'Navigate to KYC section',
        action: 'navigate',
        target: '/kyc',
        validation: 'kyc_page_loads'
      },
      {
        name: 'Start KYC verification',
        action: 'click',
        target: '[data-testid="start-kyc"]',
        validation: 'kyc_form_visible'
      },
      {
        name: 'Upload selfie',
        action: 'file_upload',
        target: '[data-testid="selfie-upload"]',
        data: 'test_selfie_image',
        validation: 'selfie_uploaded'
      },
      {
        name: 'Upload passport',
        action: 'file_upload',
        target: '[data-testid="passport-upload"]',
        data: 'test_passport_image',
        validation: 'passport_uploaded'
      },
      {
        name: 'Submit KYC documents',
        action: 'click',
        target: '[data-testid="submit-kyc"]',
        validation: 'kyc_submitted'
      },
      {
        name: 'Check verification status',
        action: 'wait_and_check',
        target: '[data-testid="kyc-status"]',
        timeout: 30000,
        validation: 'verification_processed'
      }
    ],
    expectedDuration: 180000, // 3 minutes
    cleanup: 'reset_kyc_status'
  },

  assetTokenization: {
    name: 'Asset Tokenization Workflow',
    description: 'Complete asset creation, approval, and management',
    roles: ['manager', 'client'],
    prerequisites: ['authenticated_manager', 'linked_client'],
    steps: [
      {
        name: 'Navigate to asset management',
        action: 'navigate',
        target: '/tokenised-assets',
        validation: 'assets_page_loads'
      },
      {
        name: 'Open asset creation modal',
        action: 'click',
        target: '[data-testid="create-asset-button"]',
        validation: 'asset_modal_visible'
      },
      {
        name: 'Fill asset details',
        action: 'form_fill',
        target: '[data-testid="asset-form"]',
        data: 'sample_asset_data',
        validation: 'asset_form_completed'
      },
      {
        name: 'Upload supporting documents',
        action: 'file_upload',
        target: '[data-testid="documents-upload"]',
        data: 'test_documents',
        validation: 'documents_uploaded'
      },
      {
        name: 'Submit asset for approval',
        action: 'click',
        target: '[data-testid="submit-asset"]',
        validation: 'asset_submitted'
      },
      {
        name: 'Manager approves asset',
        action: 'manager_action',
        target: 'approve_asset',
        validation: 'asset_approved'
      },
      {
        name: 'Verify asset in portfolio',
        action: 'navigate',
        target: '/dashboard',
        validation: 'asset_in_portfolio'
      }
    ],
    expectedDuration: 240000, // 4 minutes
    cleanup: 'delete_test_asset'
  },

  clientManagerInteraction: {
    name: 'Client-Manager Relationship',
    description: 'Complete client-manager linking and interaction workflow',
    roles: ['manager', 'client'],
    prerequisites: ['authenticated_manager', 'authenticated_client'],
    steps: [
      {
        name: 'Manager searches for client',
        action: 'search',
        target: '[data-testid="client-search"]',
        data: 'test_client_email',
        validation: 'client_found'
      },
      {
        name: 'Manager sends link request',
        action: 'click',
        target: '[data-testid="send-link-request"]',
        validation: 'link_request_sent'
      },
      {
        name: 'Client receives notification',
        action: 'check_notification',
        role: 'client',
        validation: 'link_notification_received'
      },
      {
        name: 'Client approves link request',
        action: 'approve_notification',
        role: 'client',
        validation: 'link_approved'
      },
      {
        name: 'Manager views client dashboard',
        action: 'navigate',
        target: '/clients',
        role: 'manager',
        validation: 'client_in_list'
      },
      {
        name: 'Manager opens client summary',
        action: 'click',
        target: '[data-testid="client-summary"]',
        validation: 'client_summary_visible'
      }
    ],
    expectedDuration: 180000, // 3 minutes
    cleanup: 'unlink_test_relationship'
  },

  messagingWorkflow: {
    name: 'Messaging Communication',
    description: 'Complete messaging workflow between users',
    roles: ['manager', 'client'],
    prerequisites: ['linked_client_manager'],
    steps: [
      {
        name: 'Manager opens messaging',
        action: 'navigate',
        target: '/messages',
        role: 'manager',
        validation: 'messages_page_loads'
      },
      {
        name: 'Manager composes message',
        action: 'click',
        target: '[data-testid="compose-message"]',
        validation: 'compose_modal_visible'
      },
      {
        name: 'Manager fills message details',
        action: 'form_fill',
        target: '[data-testid="message-form"]',
        data: 'test_message_data',
        validation: 'message_form_completed'
      },
      {
        name: 'Manager sends message',
        action: 'click',
        target: '[data-testid="send-message"]',
        validation: 'message_sent'
      },
      {
        name: 'Client receives message notification',
        action: 'check_notification',
        role: 'client',
        validation: 'message_notification_received'
      },
      {
        name: 'Client opens messages',
        action: 'navigate',
        target: '/messages',
        role: 'client',
        validation: 'client_messages_visible'
      },
      {
        name: 'Client reads message',
        action: 'click',
        target: '[data-testid="message-item"]',
        validation: 'message_marked_read'
      },
      {
        name: 'Client replies to message',
        action: 'reply_message',
        data: 'test_reply_data',
        validation: 'reply_sent'
      }
    ],
    expectedDuration: 150000, // 2.5 minutes
    cleanup: 'delete_test_messages'
  },

  dashboardWalkthrough: {
    name: 'Dashboard Walkthrough Experience',
    description: 'Complete dashboard tutorial and feature exploration',
    roles: ['client', 'manager'],
    prerequisites: ['first_time_user'],
    steps: [
      {
        name: 'Login and trigger walkthrough',
        action: 'login',
        data: 'new_user_credentials',
        validation: 'walkthrough_triggered'
      },
      {
        name: 'Start walkthrough',
        action: 'click',
        target: '[data-testid="start-walkthrough"]',
        validation: 'walkthrough_started'
      },
      {
        name: 'Navigate through steps',
        action: 'walkthrough_steps',
        validation: 'all_steps_completed'
      },
      {
        name: 'Complete walkthrough',
        action: 'click',
        target: '[data-testid="complete-walkthrough"]',
        validation: 'walkthrough_completed'
      },
      {
        name: 'Verify walkthrough persistence',
        action: 'refresh_page',
        validation: 'walkthrough_not_shown'
      },
      {
        name: 'Re-trigger from profile',
        action: 'navigate',
        target: '/profile',
        validation: 'profile_page_loads'
      },
      {
        name: 'Start walkthrough again',
        action: 'click',
        target: '[data-testid="restart-walkthrough"]',
        validation: 'walkthrough_restarted'
      }
    ],
    expectedDuration: 120000, // 2 minutes
    cleanup: 'reset_walkthrough_status'
  },

  sessionManagement: {
    name: 'Session Management and Extension',
    description: 'Session timeout, extension, and trust device functionality',
    roles: ['client', 'manager'],
    prerequisites: ['authenticated_user'],
    steps: [
      {
        name: 'Login with trust device option',
        action: 'login',
        data: 'user_credentials',
        options: { trustDevice: true },
        validation: 'trusted_session_created'
      },
      {
        name: 'Check session info display',
        action: 'check_element',
        target: '[data-testid="session-info"]',
        validation: 'session_time_displayed'
      },
      {
        name: 'Simulate session near expiry',
        action: 'simulate_time_passage',
        data: { hours: 23 },
        validation: 'extension_button_visible'
      },
      {
        name: 'Extend session',
        action: 'click',
        target: '[data-testid="extend-session"]',
        validation: 'session_extended'
      },
      {
        name: 'Verify extended session',
        action: 'check_element',
        target: '[data-testid="session-info"]',
        validation: 'new_expiry_time_shown'
      },
      {
        name: 'Simulate session expiry',
        action: 'simulate_time_passage',
        data: { hours: 24 },
        validation: 'auto_logout_triggered'
      }
    ],
    expectedDuration: 90000, // 1.5 minutes
    cleanup: 'clear_session_data'
  }
};

// Helper function to get flows by role
export function getFlowsByRole(role) {
  return Object.entries(userFlows)
    .filter(([_, flow]) => flow.roles.includes(role))
    .reduce((acc, [key, flow]) => {
      acc[key] = flow;
      return acc;
    }, {});
}

// Helper function to get flows with specific prerequisites
export function getFlowsWithPrerequisites(prerequisite) {
  return Object.entries(userFlows)
    .filter(([_, flow]) => flow.prerequisites?.includes(prerequisite))
    .reduce((acc, [key, flow]) => {
      acc[key] = flow;
      return acc;
    }, {});
}

// Helper function to get all flow names
export function getAllFlowNames() {
  return Object.keys(userFlows);
}

// Helper function to validate flow definition
export function validateFlow(flowName) {
  const flow = userFlows[flowName];
  if (!flow) {
    throw new Error(`Flow '${flowName}' not found`);
  }

  const required = ['name', 'description', 'roles', 'steps'];
  const missing = required.filter(field => !flow[field]);
  
  if (missing.length > 0) {
    throw new Error(`Flow '${flowName}' missing required fields: ${missing.join(', ')}`);
  }

  return true;
}

export default userFlows;
