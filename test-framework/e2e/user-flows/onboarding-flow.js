import { test, expect } from '@playwright/test';
import { testConfig } from '../../config/test-config.js';
import { TestDataGenerator } from '../../utils/data-generators.js';
import { TestHelpers } from '../../utils/test-helpers.js';

test.describe('User Onboarding Flow', () => {
  let dataGenerator;
  let helpers;
  let testUser;

  test.beforeAll(async () => {
    dataGenerator = new TestDataGenerator();
    helpers = new TestHelpers();
  });

  test.afterAll(async () => {
    await helpers.cleanupTestData();
  });

  test('Complete client onboarding flow', async ({ page }) => {
    // Generate test user data
    testUser = await helpers.createTestUser('client');

    // Step 1: Visit landing page
    await test.step('Visit landing page', async () => {
      await page.goto(testConfig.frontend.baseUrl);
      await expect(page).toHaveTitle(/Investor App/);
      
      // Verify landing page elements
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('[data-testid="register-button"]')).toBeVisible();
    });

    // Step 2: Navigate to registration
    await test.step('Navigate to registration', async () => {
      await page.click('[data-testid="register-button"]');
      await expect(page).toHaveURL(/.*\/register/);
      await expect(page.locator('[data-testid="registration-form"]')).toBeVisible();
    });

    // Step 3: Fill registration form
    await test.step('Fill registration form', async () => {
      await page.fill('[data-testid="contact-name-input"]', testUser.contactName);
      await page.fill('[data-testid="email-input"]', testUser.email);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.fill('[data-testid="confirm-password-input"]', testUser.password);
      await page.fill('[data-testid="tax-id-input"]', testUser.taxId);
      await page.fill('[data-testid="phone-input"]', testUser.phone);
      
      // Select role
      await page.selectOption('[data-testid="role-select"]', 'client');
      
      // Accept terms
      await page.check('[data-testid="terms-checkbox"]');
      
      // Verify form is completed
      await expect(page.locator('[data-testid="submit-registration"]')).toBeEnabled();
    });

    // Step 4: Submit registration
    await test.step('Submit registration', async () => {
      await page.click('[data-testid="submit-registration"]');
      
      // Wait for success message or redirect
      await expect(page.locator('[data-testid="registration-success"]')).toBeVisible({ timeout: 10000 });
      
      // Should redirect to login or dashboard
      await page.waitForURL(/.*\/(login|dashboard)/, { timeout: 10000 });
    });

    // Step 5: Login with new account
    await test.step('Login with new account', async () => {
      // If redirected to dashboard, skip login
      if (page.url().includes('/dashboard')) {
        return;
      }
      
      // Otherwise, perform login
      await page.goto(`${testConfig.frontend.baseUrl}/login`);
      await page.fill('[data-testid="email-input"]', testUser.email);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.click('[data-testid="login-button"]');
      
      // Wait for dashboard
      await expect(page).toHaveURL(/.*\/dashboard/);
    });

    // Step 6: Verify dashboard access
    await test.step('Verify dashboard access', async () => {
      await expect(page.locator('[data-testid="dashboard-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="user-profile-tile"]')).toBeVisible();
      await expect(page.locator('[data-testid="balance-summary"]')).toBeVisible();
      
      // Verify user information is displayed
      await expect(page.locator('[data-testid="user-name"]')).toContainText(testUser.contactName);
    });

    // Step 7: Complete profile setup (if required)
    await test.step('Complete profile setup', async () => {
      // Check if profile completion is required
      const profileIncomplete = await page.locator('[data-testid="complete-profile-banner"]').isVisible();
      
      if (profileIncomplete) {
        await page.click('[data-testid="complete-profile-button"]');
        await expect(page).toHaveURL(/.*\/profile/);
        
        // Fill additional profile information
        await page.fill('[data-testid="address-input"]', '123 Test Street, Test City');
        await page.selectOption('[data-testid="country-select"]', 'US');
        await page.click('[data-testid="save-profile-button"]');
        
        // Verify profile completion
        await expect(page.locator('[data-testid="profile-success"]')).toBeVisible();
      }
    });

    // Step 8: Verify onboarding completion
    await test.step('Verify onboarding completion', async () => {
      await page.goto(`${testConfig.frontend.baseUrl}/dashboard`);
      
      // Should not show onboarding prompts
      await expect(page.locator('[data-testid="complete-profile-banner"]')).not.toBeVisible();
      
      // Should show full dashboard functionality
      await expect(page.locator('[data-testid="asset-summary-tile"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-activity-tile"]')).toBeVisible();
    });
  });

  test('Complete manager onboarding flow', async ({ page }) => {
    // Generate test manager data
    testUser = await helpers.createTestUser('manager');

    // Follow similar steps but with manager-specific elements
    await test.step('Visit landing page and register as manager', async () => {
      await page.goto(testConfig.frontend.baseUrl);
      await page.click('[data-testid="register-button"]');
      
      // Fill manager registration form
      await page.fill('[data-testid="contact-name-input"]', testUser.contactName);
      await page.fill('[data-testid="email-input"]', testUser.email);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.fill('[data-testid="confirm-password-input"]', testUser.password);
      await page.fill('[data-testid="tax-id-input"]', testUser.taxId);
      await page.fill('[data-testid="phone-input"]', testUser.phone);
      
      // Select manager role
      await page.selectOption('[data-testid="role-select"]', 'manager');
      
      // Manager-specific fields
      await page.fill('[data-testid="company-name-input"]', 'Test Investment Company');
      await page.fill('[data-testid="license-number-input"]', 'LIC123456');
      
      await page.check('[data-testid="terms-checkbox"]');
      await page.click('[data-testid="submit-registration"]');
    });

    await test.step('Verify manager dashboard features', async () => {
      await expect(page).toHaveURL(/.*\/dashboard/);
      
      // Manager-specific dashboard elements
      await expect(page.locator('[data-testid="client-management-tile"]')).toBeVisible();
      await expect(page.locator('[data-testid="asset-approval-queue"]')).toBeVisible();
      await expect(page.locator('[data-testid="manager-tools"]')).toBeVisible();
    });
  });

  test('Handle registration errors gracefully', async ({ page }) => {
    await test.step('Test duplicate email error', async () => {
      await page.goto(`${testConfig.frontend.baseUrl}/register`);
      
      // Use existing test user email
      await page.fill('[data-testid="email-input"]', testConfig.testData.users.client.email);
      await page.fill('[data-testid="password-input"]', 'TestPassword123!');
      await page.fill('[data-testid="confirm-password-input"]', 'TestPassword123!');
      await page.fill('[data-testid="contact-name-input"]', 'Test User');
      await page.fill('[data-testid="tax-id-input"]', '*********');
      await page.fill('[data-testid="phone-input"]', '*********0');
      await page.selectOption('[data-testid="role-select"]', 'client');
      await page.check('[data-testid="terms-checkbox"]');
      
      await page.click('[data-testid="submit-registration"]');
      
      // Should show error message
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText('email');
    });

    await test.step('Test password validation', async () => {
      await page.goto(`${testConfig.frontend.baseUrl}/register`);
      
      // Use weak password
      await page.fill('[data-testid="password-input"]', '123');
      await page.fill('[data-testid="confirm-password-input"]', '123');
      
      // Should show password strength indicator
      await expect(page.locator('[data-testid="password-strength"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-strength"]')).toContainText('weak');
      
      // Submit button should be disabled
      await expect(page.locator('[data-testid="submit-registration"]')).toBeDisabled();
    });
  });

  test('Verify responsive design during onboarding', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await test.step('Mobile registration form', async () => {
      await page.goto(`${testConfig.frontend.baseUrl}/register`);
      
      // Form should be responsive
      await expect(page.locator('[data-testid="registration-form"]')).toBeVisible();
      
      // Mobile-specific elements
      const mobileMenu = page.locator('[data-testid="mobile-menu-toggle"]');
      if (await mobileMenu.isVisible()) {
        await expect(mobileMenu).toBeVisible();
      }
    });

    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    
    await test.step('Tablet dashboard layout', async () => {
      // Login first
      await page.goto(`${testConfig.frontend.baseUrl}/login`);
      await page.fill('[data-testid="email-input"]', testConfig.testData.users.client.email);
      await page.fill('[data-testid="password-input"]', testConfig.testData.users.client.password);
      await page.click('[data-testid="login-button"]');
      
      await expect(page).toHaveURL(/.*\/dashboard/);
      
      // Dashboard should adapt to tablet layout
      await expect(page.locator('[data-testid="dashboard-container"]')).toBeVisible();
    });
  });

  test('Verify accessibility during onboarding', async ({ page }) => {
    await test.step('Check form accessibility', async () => {
      await page.goto(`${testConfig.frontend.baseUrl}/register`);
      
      // Check for proper labels
      await expect(page.locator('label[for="email"]')).toBeVisible();
      await expect(page.locator('label[for="password"]')).toBeVisible();
      
      // Check for ARIA attributes
      const emailInput = page.locator('[data-testid="email-input"]');
      await expect(emailInput).toHaveAttribute('aria-required', 'true');
      
      // Check keyboard navigation
      await page.keyboard.press('Tab');
      await expect(page.locator('[data-testid="contact-name-input"]')).toBeFocused();
    });
  });
});
