# Investor App Frontend Documentation

## Overview

The Investor App Frontend is a modern, responsive web application built with Vue.js 3 that provides a sophisticated interface for investment management, asset tokenization, and client relationship management. This documentation provides a comprehensive guide to the frontend architecture, components, and implementation strategies.

## Table of Contents

- [Technology Stack](#technology-stack)
- [Architecture](#architecture)
- [Key Features](#key-features)
- [State Management](#state-management)
- [Routing](#routing)
- [Authentication](#authentication)
- [UI/UX Design](#uiux-design)
- [Components](#components)
- [Services](#services)
- [Utilities](#utilities)
- [Testing](#testing)
- [Build and Deployment](#build-and-deployment)
- [Best Practices](#best-practices)

## Technology Stack

### Core Technologies

- **Vue.js 3**: Chosen for its Composition API, which provides better TypeScript integration, code organization, and reusability compared to the Options API.
- **Vite**: Selected as the build tool for its fast development server and optimized production builds.
- **Pinia**: Used for state management, replacing Vuex for its TypeScript support, simpler API, and better developer experience.
- **Vue Router**: Handles navigation with features like route guards for authentication.
- **Bootstrap 5**: Provides responsive UI components with customization for financial/DeFi aesthetics.
- **SCSS**: Used for custom styling with variables, mixins, and nested rules.

### Additional Libraries

- **ApexCharts**: Chosen for data visualization due to its rich feature set for financial charts.
- **Vue-Toastification**: Provides toast notifications with a clean API.
- **Axios**: Handles HTTP requests with interceptors for authentication.
- **vue-web-cam**: Enables camera integration for KYC verification.
- **face-api.js**: Provides face detection capabilities for identity verification.
- **Tesseract.js**: Enables OCR for document scanning and data extraction.

## Architecture

The frontend follows a modular architecture organized by feature and responsibility:

```
src/
├── assets/          # Static assets (images, fonts, global styles)
├── components/      # Reusable Vue components
│   ├── common/      # Shared UI components
│   ├── dashboard/   # Dashboard-specific components
│   ├── kyc/         # KYC verification components
│   └── ...
├── directives/      # Custom Vue directives
├── router/          # Vue Router configuration
├── stores/          # Pinia stores for state management
├── services/        # API and external service integrations
├── utils/           # Utility functions and helpers
└── views/           # Page components
```

### Design Decisions

- **Component-Based Architecture**: The application is built with reusable components to maintain consistency and reduce duplication.
- **Composition API**: Used throughout the application for better code organization and type safety.
- **Lazy Loading**: Routes are lazy-loaded to improve initial load time and performance.
- **Responsive Design**: All components are designed to work across desktop and mobile devices.
- **Theme Support**: Dark and light theme implementation with CSS variables.

## Key Features

### User Authentication and Profile Management

- JWT-based authentication with token refresh mechanism
- Session management with "Trust this device for 24 hours" option
- Profile customization with image upload via Cloudinary
- Role-based access control (Manager/Client)

### Dashboard

- Asset portfolio overview with balance history visualization
- Recent transactions and activity tracking
- Performance metrics and analytics
- Role-specific dashboard views

### Asset Tokenization

- Digital asset creation and management
- Token issuance and transfer capabilities
- Supporting document upload and management
- Blockchain transaction tracking

### Client Management (Manager Role)

- Client onboarding and profile management
- Client-manager relationship controls
- Client portfolio overview
- Communication tools

### KYC Verification

- Live selfie capture with device camera
- Document upload and verification
- Real-time face detection
- OCR for document data extraction

### Notifications System

- Real-time notification display
- Status management (read/unread)
- Action-based notifications (approvals, rejections)
- Auto-marking of non-action notifications as viewed

### Messaging

- Client-manager communication
- Broadcast capabilities for managers
- Attachment support via Cloudinary
- Sensitive information detection

### Vesting Management

- Vesting contract creation and management
- Beneficiary management
- Schedule visualization
- Contract status tracking

### Reports and Analytics

- Asset performance tracking
- Balance history visualization
- Data export capabilities
- Custom report generation

## State Management

The application uses Pinia for state management, with stores organized by domain:

### Key Stores

- **`auth.js`**: Handles authentication state, token management, and session validation
- **`profile.js`**: Manages user profile data and updates
- **`notifications.js`**: Handles notification state and operations
- **`messages.js`**: Manages messaging functionality
- **`kyc.js`**: Handles KYC verification state and process
- **`walkthrough.js`**: Manages the dashboard walkthrough experience

### Store Design Principles

- **Domain Separation**: Each store focuses on a specific domain of the application
- **Composition**: Stores can use other stores when needed (e.g., auth store used by profile store)
- **Persistence**: Critical state is persisted in localStorage with proper serialization/deserialization
- **Reactivity**: Leverages Vue's reactivity system for efficient updates

## Routing

The application uses Vue Router for navigation with the following features:

### Route Configuration

- **Public Routes**: Landing page, login, registration, and public information pages
- **Protected Routes**: Dashboard, profile, assets, and other authenticated features
- **Role-Based Routes**: Manager-specific routes like client management

### Navigation Guards

- Authentication checks before accessing protected routes
- Role validation for role-specific routes
- Session validity verification
- Redirection to login with return path

### Route Meta Fields

- `requiresAuth`: Indicates if a route requires authentication
- `requiresManager`: Specifies manager-only routes
- `layout`: Determines the layout to use (default, public)
- `title`: Sets the page title

## Authentication

The authentication system is built with security and user experience in mind:

### Features

- **JWT-based Authentication**: Secure token-based authentication
- **Token Refresh**: Automatic token refresh to maintain session
- **Session Management**: 24-hour sessions with "Trust this device" option
- **Session Extension**: Ability to extend sessions before expiration
- **Session Monitoring**: Real-time session status display
- **Secure Storage**: Tokens stored in localStorage with proper security measures

### Implementation

- **Login Flow**: Credentials validation, token acquisition, and user profile fetching
- **Session Validation**: Regular checks to ensure token validity
- **Logout Handling**: Proper token and state cleanup
- **Auth Guards**: Route protection based on authentication state
- **HTTP Interceptors**: Automatic token inclusion in API requests

## UI/UX Design

The UI/UX design follows modern web3/DeFi aesthetics with a focus on usability:

### Design Principles

- **Clean and Modern**: Simple, smart design without unnecessary elements
- **Responsive**: Works seamlessly across devices and screen sizes
- **Consistent**: Uniform design language throughout the application
- **Accessible**: Follows accessibility best practices
- **Theme Support**: Dark and light theme options

### Visual Elements

- **Color Palette**: Blue-based scheme aligned with financial/DeFi aesthetics
- **Typography**: Clean, readable fonts with proper hierarchy
- **Icons**: Bootstrap icons for consistency
- **Cards and Containers**: Clean borders with subtle shadows
- **Charts and Visualizations**: Consistent styling with the overall design

### Interaction Design

- **Feedback**: Immediate feedback for user actions via toast notifications
- **Loading States**: Clear indication of loading processes
- **Error Handling**: User-friendly error messages
- **Transitions**: Smooth transitions between states and pages
- **Guided Experiences**: Walkthrough for new users

## Components

The application includes various reusable components:

### Core Components

- **`NavBar.vue`**: Main navigation component with responsive design
- **`ProfileTile.vue`**: User profile display component
- **`BalanceHistoryApex.vue`**: Chart component for balance visualization
- **`NotificationsModal.vue`**: Notifications display and management
- **`SessionInfoCard.vue`**: Session information display
- **`SessionExtensionButton.vue`**: Session extension functionality
- **`TokeniseAssetModal.vue`**: Asset tokenization interface
- **`DashboardWalkthrough.vue`**: Interactive dashboard tutorial

### Component Design Principles

- **Reusability**: Components designed for reuse across the application
- **Composability**: Smaller components combined to create complex interfaces
- **Prop Validation**: Clear prop interfaces with validation
- **Event Handling**: Consistent event emission patterns
- **Slot Usage**: Flexible content insertion via slots

## Services

The application includes various services for external integrations:

### API Services

- **Authentication API**: User authentication and session management
- **Profile API**: User profile management
- **Assets API**: Asset tokenization and management
- **Notifications API**: Notification handling
- **Messaging API**: Communication between clients and managers

### External Integrations

- **Cloudinary**: Image and file storage
- **Blockchain Integration**: Asset tokenization and verification
- **OCR Services**: Document scanning and data extraction

## Utilities

The application includes various utility functions and helpers:

### Key Utilities

- **`modalUtils.js`**: Handles modal creation and cleanup
- **`formatters.js`**: Data formatting utilities (currency, dates, etc.)
- **`validators.js`**: Form validation helpers
- **`tokenUtils.js`**: JWT token handling utilities
- **`themeUtils.js`**: Theme switching and management

## Testing

The application includes a testing strategy with:

### Testing Approaches

- **Unit Tests**: Testing individual components and functions
- **Component Tests**: Testing component behavior and rendering
- **E2E Tests**: Testing complete user flows

### Testing Tools

- **Vitest**: Unit and component testing
- **Cypress**: End-to-end testing
- **Testing Library**: Component testing utilities
- **Comprehensive Test Suite**: Systematic testing framework for all capabilities

## Build and Deployment

The application uses Vite for building and deployment:

### Build Process

- **Development**: Fast development server with hot module replacement
- **Production**: Optimized builds with code splitting and tree shaking
- **Environment Variables**: Configuration via environment variables

### Deployment Strategies

- **Static Hosting**: Deployment to static hosting services
- **CI/CD Integration**: Automated testing and deployment
- **Environment-Specific Builds**: Different builds for development, staging, and production

## Best Practices

The codebase follows these best practices:

### Code Quality

- **ESLint and Prettier**: Consistent code formatting and linting
- **TypeScript**: Type safety where applicable
- **Code Reviews**: Peer review process for code changes
- **Documentation**: Comprehensive code documentation

### Performance

- **Lazy Loading**: Components and routes loaded on demand
- **Code Splitting**: Breaking the application into smaller chunks
- **Asset Optimization**: Image and asset optimization
- **Caching Strategies**: Proper caching of API responses and assets

### Security

- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: Protection against cross-site request forgery
- **Secure Authentication**: Proper token handling and validation
- **Data Validation**: Client-side and server-side validation

### Accessibility

- **ARIA Attributes**: Proper accessibility attributes
- **Keyboard Navigation**: Support for keyboard navigation
- **Screen Reader Support**: Compatible with screen readers
- **Color Contrast**: Sufficient contrast for readability

---

This documentation provides a comprehensive overview of the Investor App Frontend. For specific implementation details, refer to the codebase and comments within the files.
