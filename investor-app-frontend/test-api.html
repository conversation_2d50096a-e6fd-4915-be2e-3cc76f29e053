<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connectivity Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>API Connectivity Test</h1>
    
    <div class="info test-result">
        <strong>Testing API connectivity for the deployed application</strong>
    </div>

    <button onclick="testApiConnectivity()">Test API Connectivity</button>
    <button onclick="testCorsHeaders()">Test CORS Headers</button>
    <button onclick="clearResults()">Clear Results</button>

    <div id="results"></div>

    <script>
        const API_URL = 'https://archimedes-finance-api-v2.vercel.app';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testApiConnectivity() {
            addResult('Testing basic API connectivity...', 'info');
            
            try {
                // Test basic connectivity
                const response = await fetch(`${API_URL}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.text();
                    addResult(`✅ API is reachable! Response: ${data}`, 'success');
                } else {
                    addResult(`⚠️ API responded with status: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Failed to connect to API: ${error.message}`, 'error');
                console.error('API connectivity test failed:', error);
            }

            // Test if the API root responds
            try {
                const response = await fetch(API_URL, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                addResult(`API root response status: ${response.status}`, response.ok ? 'success' : 'error');
            } catch (error) {
                addResult(`❌ Failed to connect to API root: ${error.message}`, 'error');
            }
        }

        async function testCorsHeaders() {
            addResult('Testing CORS headers...', 'info');
            
            try {
                const response = await fetch(`${API_URL}/health`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'Content-Type, Authorization'
                    }
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
                };
                
                addResult(`CORS Headers: <pre>${JSON.stringify(corsHeaders, null, 2)}</pre>`, 'info');
                
                if (corsHeaders['Access-Control-Allow-Origin']) {
                    addResult('✅ CORS headers are present', 'success');
                } else {
                    addResult('❌ CORS headers missing', 'error');
                }
                
            } catch (error) {
                addResult(`❌ CORS test failed: ${error.message}`, 'error');
            }
        }

        // Run initial test
        window.onload = function() {
            addResult(`Testing API URL: ${API_URL}`, 'info');
            addResult(`Current origin: ${window.location.origin}`, 'info');
        };
    </script>
</body>
</html>
