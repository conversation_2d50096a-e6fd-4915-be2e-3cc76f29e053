import { spawn } from 'child_process';

const tasks = [
  { name: '<PERSON><PERSON>', cmd: 'npm', args: ['run', 'lint'] },
  { name: 'Unit Tests', cmd: 'npm', args: ['run', 'test:unit', '--', '--run'] },
  { name: 'E2E Tests (dev)', cmd: 'npm', args: ['run', 'test:e2e:dev'] },
  { name: 'E2E Tests (prod)', cmd: 'npm', args: ['run', 'test:e2e'] },
];

function runTask(task) {
  return new Promise((resolve) => {
    console.log(`\n===== ${task.name} =====`);
    const proc = spawn(task.cmd, task.args, { stdio: 'inherit', shell: true });
    proc.on('close', (code) => {
      console.log(`${task.name} finished with code ${code}`);
      resolve({ name: task.name, code });
    });
  });
}

async function runAll() {
  const results = [];
  for (const task of tasks) {
     
    const result = await runTask(task);
    results.push(result);
  }

  console.log('\n===== Summary =====');
  results.forEach(({ name, code }) => {
    console.log(`${code === 0 ? '✅' : '❌'} ${name}`);
  });

  const hasFail = results.some((r) => r.code !== 0);
  process.exit(hasFail ? 1 : 0);
}

runAll();
