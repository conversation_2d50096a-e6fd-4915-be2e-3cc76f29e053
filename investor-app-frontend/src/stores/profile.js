import { defineStore } from 'pinia';
import defaultProfileImage from '@/assets/images/default-profile.png'; // import local default image
import apiClient from '@/services/apiClient';
import { useToast } from 'vue-toastification';

const toast = useToast();

export const useProfileStore = defineStore('profile', {
  state: () => ({
    user: {
      contact_name: '',
      email: '',
      phone: '',
      address: '',
      company_name: '',
      role: '',
      membership: '',
      profile_image: '',
    },
    profilePicture: null,
    // Use the imported local image as the fallback
    defaultProfilePicture: defaultProfileImage,
    passwords: {
      currentPassword: '',
      newPassword: '',
    },
    uploading: false,
    uploadFeedback: null,
    loading: false,
    error: null,
    successMessage: '',
    emailError: null,
  }),

  actions: {
    async fetchUserProfile() {
      try {
        this.loading = true;
        this.error = null;

        const res = await apiClient.get('/profile');

        if (res.ok && res.data.user) {
          // Include KYC status in user data
          this.user = {
            ...res.data.user,
            kyc_status: res.data.user.kyc_status || 'unverified'
          };
        } else {
          throw new Error('Failed to fetch profile');
        }
      } catch (error) {
        this.error = error.message || 'An error occurred while fetching profile';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateProfile() {
      try {
        const res = await apiClient.put('/profile', this.user);
        if (res.ok) {
          this.successMessage = 'Profile updated successfully!';
        } else {
          if (res.status === 409) {
            this.emailError = 'This email is already in use.';
          } else {
            this.error = res.errors[0] || 'Error updating profile';
          }
          res.errors.forEach(e => toast.error(e));
        }
      } catch (error) {
        this.error = error.message || 'Error updating profile';
        toast.error(this.error);
      }
    },

    async changePassword() {
      try {
        const res = await apiClient.put('/profile/password', this.passwords);
        if (res.ok) {
          this.successMessage = 'Password changed successfully!';
          this.passwords = { currentPassword: '', newPassword: '' };
        } else {
          this.error = res.errors[0] || 'Error changing password';
          res.errors.forEach(e => toast.error(e));
        }
      } catch (error) {
        this.error = 'Error changing password';
        toast.error(this.error);
      }
    },

    async uploadProfilePicture(event) {
      const file = event.target.files[0];

      // Ensure a file is selected and is a valid Blob
      if (!file || !(file instanceof Blob)) {
          this.uploadFeedback = { success: false, message: 'Invalid file. Please select an image file.' };
          return;
      }

      const maxSizeInBytes = 5 * 1024 * 1024;
      if (file.size > maxSizeInBytes) {
          this.uploadFeedback = { success: false, message: 'The file size must be less than 5MB.' };
          return;
      }

      const image = new Image();
      const reader = new FileReader();

      reader.onload = (e) => {
          image.src = e.target.result;
          image.onload = async () => {
              if (image.width > 1000 || image.height > 1000) {
                  this.uploadFeedback = { success: false, message: 'Image dimensions must not exceed 1000x1000 pixels.' };
                  return;
              }

              const formData = new FormData();
              formData.append('profileImage', file);

              this.uploading = true;
              this.uploadFeedback = null;

              try {
                  const res = await apiClient.post('/profile/upload', formData, {
                      headers: {
                          'Content-Type': 'multipart/form-data',
                      },
                  });

                  if (res.ok) {
                      this.profilePicture = res.data.imageUrl || this.defaultProfilePicture;
                      this.uploadFeedback = { success: true, message: 'Profile image uploaded successfully!' };
                      this.fetchUserProfile();
                  } else {
                      this.uploadFeedback = { success: false, message: res.errors[0] || 'Image upload failed.' };
                      res.errors.forEach(e => toast.error(e));
                  }
              } catch (error) {
                  this.uploadFeedback = { success: false, message: error.message || 'Image upload failed.' };
                  toast.error(this.uploadFeedback.message);
              } finally {
                  this.uploading = false;
              }
          };
      };

      try {
          reader.readAsDataURL(file);
      } catch (err) {
          console.error("FileReader error:", err);
          this.uploadFeedback = { success: false, message: 'Could not process the selected file.' };
      }
    },

    logout() {
      this.user = {
        contact_name: '',
        email: '',
        phone: '',
        address: '',
        company_name: '',
        role: '',
        membership: '',
        profile_image: '',
      };
      this.profilePicture = this.defaultProfilePicture;
    },
  },

  getters: {
    isAuthenticated: (state) => !!state.user.email,
  },
});
