import { defineStore } from 'pinia';
import { useToast } from 'vue-toastification';
import apiClient from '@/services/apiClient';

const toast = useToast();

export const useNotificationsStore = defineStore('notifications', {
    state: () => ({
        notifications: [],
    }),

    actions: {
        async fetchNotifications() {
            const res = await apiClient.get('/notifications');
            if (res.ok) {
                this.notifications = res.data.notifications || [];
            } else {
                res.errors.forEach(e => toast.error(e));
            }
        },

        async sendNotification(recipientId, type, message) {
            const res = await apiClient.post('/notifications', { recipientId, type, message });
            if (!res.ok) {
                res.errors.forEach(e => toast.error(e));
            }
            await this.fetchNotifications();
        },

        async updateNotification(id, status) {
            const res = await apiClient.put(`/notifications/${id}`, { status });
            if (!res.ok) {
                res.errors.forEach(e => toast.error(e));
            }
            await this.fetchNotifications();
        },

        async deleteNotification(id) {
            const res = await apiClient.delete(`/notifications/${id}`);
            if (!res.ok) {
                res.errors.forEach(e => toast.error(e));
            }
            await this.fetchNotifications();
        },

        async markAsRead(id) {
            await this.updateNotification(id, "read");
        },

        async markMultipleAsRead(ids) {
            if (!ids || ids.length === 0) {
                return;
            }
            const res = await apiClient.put('/notifications/batch/mark-as-read', { ids });
            if (!res.ok) {
                res.errors.forEach(e => toast.error(e));
            }
            await this.fetchNotifications();
        },

        async ignoreNotification(id) {
            await this.updateNotification(id, "ignored");
        }
    }
});
