import { defineStore } from 'pinia';
import { useToast } from 'vue-toastification';
import { useNotificationsStore } from "@/stores/notifications";
import apiClient from '@/services/apiClient';
import router from '@/router'; // Import Vue Router instance

// Helper function to format remaining time
function formatRemainingTime(milliseconds) {
  if (milliseconds <= 0) return 'Expired';

  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  const remainingMinutes = minutes % 60;

  if (hours > 0) {
    return `${hours}h ${remainingMinutes}m`;
  } else {
    return `${minutes}m`;
  }
}

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    userId: localStorage.getItem('userId') || null,
    user: null,
    lastActivity: Date.now(),
    sessionCheckInterval: null, // Store the interval reference
    rememberMe: localStorage.getItem('rememberMe') === 'true',
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,

    // Get token expiration information
    tokenExpiration: (state) => {
      if (!state.token) return null;

      try {
        // Decode the JWT token
        const tokenParts = state.token.split('.');
        if (tokenParts.length !== 3) return null;

        const payload = JSON.parse(atob(tokenParts[1]));

        // Calculate expiration time and remaining time
        const expirationTime = payload.exp * 1000; // Convert to milliseconds
        const remainingTime = expirationTime - Date.now();
        const isRememberMe = payload.rememberMe === true;

        return {
          expirationTime,
          remainingTime,
          isRememberMe,
          expirationDate: new Date(expirationTime),
          // Format remaining time as hours and minutes
          formattedRemainingTime: formatRemainingTime(remainingTime)
        };
      } catch (e) {
        console.warn('Error decoding token:', e);
        return null;
      }
    },
  },

  actions: {
    async login(email, password, rememberMe = false) {
      try {
        // Ensure rememberMe is properly passed as a boolean
        const rememberMeBool = !!rememberMe;

        const res = await apiClient.post('/auth/login', {
          email,
          password,
          rememberMe: rememberMeBool,
        });

        if (!res.ok) {
          throw new Error(res.errors[0] || 'Login failed. Please try again.');
        }

        const { token, user } = res.data;

        if (!token || !user || !user.id) {
          throw new Error("Invalid login response: Missing user ID or token.");
        }

        // Store token & user data
        localStorage.setItem('token', token);
        localStorage.setItem('userId', user.id);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('rememberMe', rememberMeBool ? 'true' : 'false');

        this.token = token;
        this.userId = user.id;
        this.user = user;
        this.lastActivity = Date.now();
        this.rememberMe = rememberMeBool;

        // Silently decode token to verify rememberMe flag is set correctly
        try {
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            // Store expiration time for reference
            new Date(payload.exp * 1000);
          }
        } catch {
          // Silently handle token decode errors
        }

        // Fetch user profile immediately after login
        await this.fetchUserProfile();

        this.startSessionCheck();

        // Fetch notifications immediately after login
        const notificationsStore = useNotificationsStore();
        await notificationsStore.fetchNotifications();
        return { success: true };
      } catch (error) {
        console.error('Login error:', error.response?.data || error.message);
        throw new Error(error.response?.data?.error || 'Login failed. Please try again.');
      }
    },

    setAuthData(token, user) {
      if (!token || !user || !user.id) {
        throw new Error("Invalid authentication data");
      }

      localStorage.setItem('token', token);
      localStorage.setItem('userId', user.id);
      localStorage.setItem('user', JSON.stringify(user));

      // Preserve the rememberMe setting
      const rememberMe = localStorage.getItem('rememberMe') === 'true';

      this.token = token;
      this.userId = user.id;
      this.user = user;
      this.lastActivity = Date.now();
      this.rememberMe = rememberMe;

      this.startSessionCheck();
    },

    async fetchUserProfile() {
      try {
        if (!this.token) {
          // Skip user profile fetch when no token is available
          return;
        }

        const res = await apiClient.get('/profile');

        if (!res.ok || !res.data.user || !res.data.user.id) {
          throw new Error("Invalid user profile response.");
        }

        this.user = res.data.user;
        localStorage.setItem('user', JSON.stringify(this.user)); // Store user details locally
        this.lastActivity = Date.now(); // ✅ Prevents immediate session expiry

      } catch (error) {
        console.error('Error fetching user profile:', error.response?.data || error.message);

        // ✅ Do NOT log out immediately unless we confirm the token is actually invalid
        if (error.response?.status === 401) {
          this.logoutAndRedirect();
        }
      }
    },

    logout() {

      // Explicitly clear stored authentication data
      this.token = null;
      this.userId = null;
      this.user = null;
      this.lastActivity = null;
      this.sessionCheckInterval = null;
      this.rememberMe = false;

      // Completely remove from localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('userId');
      localStorage.removeItem('user');
      localStorage.removeItem('notifications');
      localStorage.removeItem('rememberMe');

      // Clear session interval checks
      this.cleanupSessionCheck();
    },

    async logoutAndRedirect() {
      await this.logout();
      router.push('/auth/login'); // Ensure logout process completes before redirect
    },

    async checkSessionValidity() {
      try {
        if (!this.token) {
          this.handleAuthError('Your session has expired. Please log in again.');
          return false;
        }

        const res = await apiClient.get('/auth/check-session');

        if (!res.ok || !res.data.valid) {
          this.handleAuthError('Your session has expired. Please log in again.');
          return false;
        }

        // Update user data if it's returned from the check-session endpoint
        if (res.data.user) {
          this.user = res.data.user;
          localStorage.setItem('user', JSON.stringify(this.user));
        }

        this.lastActivity = Date.now();
        return true;
      } catch (error) {
        if (error.response?.status === 401 || error.response?.status === 403) {
          this.handleAuthError('Your session has expired. Please log in again.');
        } else {
          console.error('Session check error:', error);
        }
        return false;
      }
    },

    handleAuthError(message) {
      const toast = useToast();
      toast.error(message, {
        timeout: 5000,
        position: "top-right",
        closeButton: true,
        pauseOnHover: true,
        draggable: true,
      });
      this.logoutAndRedirect();
    },

    updateActivity() {
      this.lastActivity = Date.now();
    },

    // Get the latest token expiration information
    getTokenExpirationInfo() {
      if (!this.token) return null;

      try {
        // Decode the JWT token
        const tokenParts = this.token.split('.');
        if (tokenParts.length !== 3) return null;

        const payload = JSON.parse(atob(tokenParts[1]));

        // Calculate expiration time and remaining time
        const expirationTime = payload.exp * 1000; // Convert to milliseconds
        const remainingTime = expirationTime - Date.now();
        const isRememberMe = payload.rememberMe === true;

        return {
          expirationTime,
          remainingTime,
          isRememberMe,
          expirationDate: new Date(expirationTime),
          formattedRemainingTime: formatRemainingTime(remainingTime)
        };
      } catch (e) {
        console.warn('Error decoding token:', e);
        return null;
      }
    },

    // Extend the session for another 24 hours
    async extendSession() {
      try {

        // Call the backend to extend the session
        const res = await apiClient.post('/auth/extend-session', {});

        // If the backend returns a new token, update it
        if (res.ok && res.data.token) {
          // Store the new token
          localStorage.setItem('token', res.data.token);
          localStorage.setItem('rememberMe', 'true');

          this.token = res.data.token;
          this.rememberMe = true;
          this.lastActivity = Date.now();

          // Silently decode token to verify expiration
          try {
            const tokenParts = res.data.token.split('.');
            if (tokenParts.length === 3) {
              const payload = JSON.parse(atob(tokenParts[1]));
              // Store expiration time for reference
              new Date(payload.exp * 1000);
            }
          } catch {
            // Silently handle token decode errors
          }

          return true;
        } else {
          throw new Error('No token received from server');
        }
      } catch (error) {
        console.error('Failed to extend session:', error);
        throw error;
      }
    },

    startSessionCheck() {
      this.cleanupSessionCheck(); // Ensure previous checks are cleared before starting a new one

      window.addEventListener('focus', this.checkSessionValidity);
      window.addEventListener('mousemove', this.updateActivity);

      // Set different check intervals based on rememberMe
      // For trusted devices (rememberMe=true), check much less frequently (30 minutes)
      // For regular sessions, check every 5 minutes
      const checkInterval = this.rememberMe ? 30 * 60 * 1000 : 5 * 60 * 1000;

      this.sessionCheckInterval = setInterval(() => {
        this.checkSessionValidity();
      }, checkInterval);
    },

    cleanupSessionCheck() {
      if (this.sessionCheckInterval) {
        clearInterval(this.sessionCheckInterval);
        this.sessionCheckInterval = null;
      }

      window.removeEventListener('focus', this.checkSessionValidity);
      window.removeEventListener('mousemove', this.updateActivity);
    },
  },
});

