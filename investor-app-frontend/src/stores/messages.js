import { defineStore } from 'pinia';
import apiClient from '@/services/apiClient';
import { useAuthStore } from './auth';
import { useToast } from 'vue-toastification';

const toast = useToast();

export const useMessagesStore = defineStore('messages', {
  state: () => ({
    messages: [],
    currentMessage: null,
    loading: false,
    error: null,
    pagination: {
      total: 0,
      page: 1,
      limit: 20,
      pages: 0
    },
    currentFolder: 'inbox',
    recipients: [],
    sensitiveInfoCheck: {
      containsSensitiveInfo: false,
      warnings: [],
      isChecking: false
    }
  }),

  getters: {
    unreadCount: (state) => {
      return state.messages.filter(msg =>
        msg.status === 'unread' && state.currentFolder === 'inbox'
      ).length;
    },

    hasMessages: (state) => {
      return state.messages.length > 0;
    }
  },

  actions: {
    async fetchMessages(folder = 'inbox', page = 1, limit = 20) {
      this.loading = true;
      this.error = null;
      this.currentFolder = folder;

      const res = await apiClient.get('/messages', { params: { folder, page, limit } });
      this.loading = false;
      if (res.ok) {
        this.messages = res.data.messages;
        this.pagination = res.data.pagination;
      } else {
        this.error = res.errors[0] || 'Failed to load messages';
        res.errors.forEach(e => toast.error(e));
      }
    },

    async fetchMessageDetails(messageId) {
      this.loading = true;
      this.error = null;

      const res = await apiClient.get(`/messages/action/${messageId}`);
      this.loading = false;
      if (res.ok) {
        this.currentMessage = res.data;
        return res.data;
      } else {
        console.error('Error fetching message details:', res.errors);
        this.error = res.errors[0] || 'Failed to load message details';
        res.errors.forEach(e => toast.error(e));
        return null;
      }
    },

    async sendMessage(messageData, attachments = []) {
      this.loading = true;
      this.error = null;

      try {
        // Create form data for file uploads
        const formData = new FormData();
        formData.append('subject', messageData.subject);
        formData.append('body', messageData.body);
        formData.append('recipientIds', JSON.stringify(messageData.recipientIds));
        formData.append('isBroadcast', messageData.isBroadcast || false);

        // Add attachments if any
        if (attachments && attachments.length > 0) {
          attachments.forEach(file => {
            formData.append('attachments', file);
          });
        }

        const res = await apiClient.post('/messages', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        if (res.ok) {
          toast.success('Message sent successfully');
          if (res.data.containsSensitiveInfo) {
            toast.warning('Your message was sent but contains potentially sensitive information');
          }
          return res.data;
        } else {
          this.error = res.errors[0] || 'Failed to send message';
          res.errors.forEach(e => toast.error(e));
          return null;
        }
      } catch (error) {
        console.error('Error sending message:', error);
        this.error = error.message || 'Failed to send message';
        toast.error(this.error);
        return null;
      } finally {
        this.loading = false;
      }
    },

    async markAsRead(messageId) {
      const res = await apiClient.put(`/messages/action/${messageId}/read`, {});
      if (!res.ok) {
        console.error('Error marking message as read:', res.errors);
      }

      const index = this.messages.findIndex(msg => msg.id === parseInt(messageId));
      if (index !== -1) {
        this.messages[index].status = 'read';
      }
    },

    async archiveMessage(messageId) {
      this.loading = true;

      try {
        const res = await apiClient.delete(`/messages/action/${messageId}`);
        if (res.ok) {
          this.messages = this.messages.filter(msg => msg.id !== parseInt(messageId));
          toast.success('Message archived');
        } else {
          res.errors.forEach(e => toast.error(e));
        }
      } catch (error) {
        console.error('Error archiving message:', error);
        toast.error(error.message || 'Failed to archive message');
      } finally {
        this.loading = false;
      }
    },

    async fetchRecipients() {
      this.loading = true;

      try {
        const authStore = useAuthStore();
        const userRole = authStore.user?.role;

        let endpoint = '/messages/clients'; // Default for managers
        if (userRole === 'client') {
          endpoint = '/messages/manager';
        }

        const res = await apiClient.get(endpoint);
        if (res.ok) {
          if (userRole === 'client') {
            this.recipients = res.data ? [res.data] : [];
          } else {
            this.recipients = res.data || [];
          }
        } else {
          res.errors.forEach(e => toast.error(e));
        }

        return this.recipients;
      } catch (error) {
        console.error('Error fetching recipients:', error);
        toast.error('Failed to load message recipients. Please try again.');
        return [];
      } finally {
        this.loading = false;
      }
    },

    async checkSensitiveInfo(text) {
      if (!text || text.trim().length < 10) {
        this.sensitiveInfoCheck = {
          containsSensitiveInfo: false,
          warnings: [],
          isChecking: false
        };
        return this.sensitiveInfoCheck;
      }

      this.sensitiveInfoCheck.isChecking = true;

      try {
        const res = await apiClient.post('/messages/check-sensitive', { text });
        if (res.ok) {
          this.sensitiveInfoCheck = { ...res.data, isChecking: false };
        } else {
          this.sensitiveInfoCheck = {
            containsSensitiveInfo: false,
            warnings: [],
            isChecking: false
          };
          res.errors.forEach(e => toast.error(e));
        }
        return this.sensitiveInfoCheck;
      } catch (error) {
        console.error('Error checking sensitive info:', error);
        this.sensitiveInfoCheck = {
          containsSensitiveInfo: false,
          warnings: [],
          isChecking: false
        };
        return this.sensitiveInfoCheck;
      }
    },

    resetSensitiveInfoCheck() {
      this.sensitiveInfoCheck = {
        containsSensitiveInfo: false,
        warnings: [],
        isChecking: false
      };
    }
  }
});
