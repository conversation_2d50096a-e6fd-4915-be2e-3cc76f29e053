import { defineStore } from 'pinia';
import { useToast } from 'vue-toastification';
import apiClient from '@/services/apiClient';

const toast = useToast();

export const useKycStore = defineStore('kyc', {
  state: () => ({
    status: null,
    loading: false,
    error: null,
    verificationHistory: [],
  }),

  getters: {
    isVerified: (state) => state.status === 'verified',
    isPending: (state) => state.status === 'pending',
    isFailed: (state) => state.status === 'failed',
    hasNotStarted: (state) => !state.status || state.status === 'not_started',
  },

  actions: {
    async fetchKycStatus() {
      this.loading = true;
      this.error = null;
      const res = await apiClient.get('/kyc/status');
      this.loading = false;
      if (res.ok) {
        this.status = res.data.status;
        return res.data;
      } else {
        this.error = res.errors[0] || 'Failed to fetch KYC status';
        toast.error(this.error);
        throw new Error(this.error);
      }
    },

    async submitKycVerification(formData) {
      this.loading = true;
      this.error = null;
      const res = await apiClient.post('/kyc/verify', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      this.loading = false;
      if (res.ok) {
        this.status = res.data.status;
        return res.data;
      } else {
        this.error = res.errors[0] || 'Failed to submit KYC verification';
        toast.error(this.error);
        throw new Error(this.error);
      }
    },

    async fetchVerificationHistory() {
      this.loading = true;
      this.error = null;
      const res = await apiClient.get('/kyc/history');
      this.loading = false;
      if (res.ok) {
        this.verificationHistory = res.data;
        return res.data;
      } else {
        this.error = res.errors[0] || 'Failed to fetch verification history';
        toast.error(this.error);
        throw new Error(this.error);
      }
    },

    reset() {
      this.status = null;
      this.loading = false;
      this.error = null;
      this.verificationHistory = [];
    },
  },
});
