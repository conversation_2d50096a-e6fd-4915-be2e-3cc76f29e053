<template>
  <div class="container kyc-view">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title mb-0">Identity Verification</h2>
          </div>
          <div class="card-body">
            <!-- Loading State -->
            <div v-if="loading" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>

            <!-- Error State -->
            <div v-else-if="error" class="alert alert-danger">
              {{ error }}
            </div>

            <!-- Verified State -->
            <div v-else-if="verificationStatus?.status === 'verified'" class="verification-info">
              <div class="text-center mb-4">
                <i class="bi bi-check-circle-fill text-success display-1"></i>
                <h3 class="mt-3">Identity Verified</h3>
                <p class="text-muted">
                  Your identity was verified on {{ formatDate(verificationStatus.updated_at) }}
                </p>
              </div>

              <div class="verification-details">
                <h4>Verification Details</h4>
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label">Status</label>
                      <p class="mb-0"><span class="badge bg-success">Verified</span></p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label">Last Updated</label>
                      <p class="mb-0">{{ formatDate(verificationStatus.updated_at) }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Failed State -->
            <div v-else-if="verificationStatus?.status === 'failed'" class="verification-failed">
              <div class="text-center mb-4">
                <i class="bi bi-x-circle-fill text-danger display-1"></i>
                <h3 class="mt-3">Verification Failed</h3>
                <p class="text-muted">
                  Your previous verification attempt was unsuccessful.
                </p>
              </div>

              <div class="alert alert-warning">
                <h5>Verification Details</h5>
                <p class="mb-0">{{ verificationStatus?.metadata?.message || 'Please try the verification process again.' }}</p>
              </div>

              <div class="text-center mt-4">
                <button class="btn btn-primary" @click="startVerification">
                  Try Again
                </button>
              </div>
            </div>

            <!-- Pending State -->
            <div v-else-if="verificationStatus?.status === 'pending'" class="verification-pending">
              <div class="text-center mb-4">
                <i class="bi bi-clock-fill text-warning display-1"></i>
                <h3 class="mt-3">Verification in Progress</h3>
                <p class="text-muted">
                  Your verification is being processed. Please check back later.
                </p>
              </div>

              <div class="alert alert-info">
                <h5>Status</h5>
                <p class="mb-0">Your verification request was submitted on {{ formatDate(verificationStatus.submitted_at) }}</p>
              </div>
            </div>

            <!-- Unverified State -->
            <div v-else class="verification-start">
              <div class="text-center mb-4">
                <i class="bi bi-person-check display-1 text-primary"></i>
                <h3 class="mt-3">Verify Your Identity</h3>
                <p class="text-muted">
                  Complete the verification process to access additional features and ensure account security.
                </p>
              </div>

              <div class="verification-steps">
                <div class="step mb-4">
                  <h5>Step 1: Upload Passport</h5>
                  <p>Upload a clear photo of your passport's information page.</p>
                </div>
                <div class="step mb-4">
                  <h5>Step 2: Take a Selfie</h5>
                  <p>Take a clear selfie photo in good lighting conditions.</p>
                </div>
                <div class="step mb-4">
                  <h5>Step 3: Verification</h5>
                  <p>Our system will verify your identity using advanced facial recognition technology.</p>
                </div>
              </div>

              <div class="text-center mt-4">
                <button class="btn btn-primary" @click="startVerification">
                  Begin Verification
                </button>
              </div>
            </div>

            <!-- KYC Verification Component -->
            <div v-if="showVerification" class="mt-4">
              <KycVerification @verification-submitted="handleVerificationSubmitted" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import apiClient from '@/services/apiClient';
import { useAuthStore } from '@/stores/auth';
import KycVerification from '@/components/kyc/KycVerification.vue';

const router = useRouter();
const authStore = useAuthStore();

const loading = ref(true);
const error = ref(null);
const verificationStatus = ref(null);
const showVerification = ref(false);

// Fetch verification status
const fetchVerificationStatus = async () => {
  try {
    loading.value = true;
    error.value = null;

    const res = await apiClient.get('/kyc/status');
    if (res.ok && res.data.success) {
      verificationStatus.value = res.data.data;
    } else {
      throw new Error(res.errors?.[0] || res.data.message || 'Failed to fetch verification status');
    }
  } catch (err) {
    error.value = err.message || 'An error occurred while fetching verification status';
  } finally {
    loading.value = false;
  }
};

// Start verification process
const startVerification = () => {
  showVerification.value = true;
};

// Handle verification submission
const handleVerificationSubmitted = async () => {
  await fetchVerificationStatus();
  showVerification.value = false;
};

// Format date for display
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

onMounted(() => {
  fetchVerificationStatus();
});
</script>

<style scoped>
.kyc-view {
  padding: 2rem 0;
}

.verification-info,
.verification-failed,
.verification-pending,
.verification-start {
  padding: 2rem 0;
}

.verification-details {
  background-color: var(--bs-card2-bg);
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-top: 2rem;
}

.verification-steps {
  background-color: var(--bs-card2-bg);
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-top: 2rem;
}

.step {
  padding-left: 2rem;
  position: relative;
}

.step::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 1rem;
  height: 1rem;
  background-color: var(--bs-primary);
  border-radius: 50%;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 0.45rem;
  top: 1.5rem;
  width: 0.1rem;
  height: 2rem;
  background-color: var(--bs-primary);
}
</style>
