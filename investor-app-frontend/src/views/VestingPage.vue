<template>
  <div class="container mt-5">
    <h2 class="text-center mb-4">Vesting Management</h2>

    <!-- 🔹 Loading State -->
    <div v-if="isLoading" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- 🔹 Error State -->
    <div v-else-if="error" class="alert alert-danger" role="alert">
      {{ error }}
    </div>

    <!-- 🔹 Content -->
    <template v-else>
      <!-- 🔹 Vesting Overview Card -->
      <div class="card mb-4">
        <div class="card-body">
          <h5 class="card-title">Vesting Overview</h5>
          <div class="row">
            <div class="col-md-4">
              <div class="vesting-stat">
                <h6>Total Vesting Contracts</h6>
                <p class="h3">{{ totalContracts }}</p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="vesting-stat">
                <h6>Active Vestings</h6>
                <p class="h3">{{ activeVestings }}</p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="vesting-stat">
                <h6>Total Value Locked</h6>
                <p class="h3">${{ totalValueLocked.toLocaleString() }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 🔹 Vesting Contracts List -->
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="card-title mb-0">Vesting Contracts</h5>
            <div class="d-flex gap-2">
              <!-- Create Vesting Contract Button -->
              <router-link v-if="isManager" to="/vesting/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>Create Vesting Contract
              </router-link>
              <!-- Refresh Button -->
              <button @click="fetchVestingContracts" class="btn btn-outline-primary">
                <i class="bi bi-arrow-clockwise"></i> Refresh
              </button>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table modern-table">
              <thead>
                <tr>
                  <th>Asset Name</th>
                  <th v-if="isManager">Beneficiary</th>
                  <th>Contract Address</th>
                  <th>Start Date</th>
                  <th>End Date</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="contract in vestingContracts" :key="contract.id">
                  <td>{{ contract.asset_name }}</td>
                  <td v-if="isManager">{{ listBeneficiaries(contract.beneficiaries) }}</td>
                  <td>
                    <code>{{ contract.contract_address }}</code>
                  </td>
                  <td>{{ formatDate(contract.vesting_schedule.start_date) }}</td>
                  <td>{{ formatDate(contract.vesting_schedule.end_date) }}</td>
                  <td>
                    <span :class="`status-badge ${getStatusClass(contract.status)}`">
                      {{ contract.status }}
                    </span>
                  </td>
                  <td>
                    <div class="btn-group">
                      <button @click="viewContractDetails(contract)" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button v-if="isManager && contract.status === 'pending'"
                              @click="approveContract(contract.id)"
                              class="btn btn-sm btn-outline-success">
                        <i class="bi bi-check-lg"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <p v-if="!vestingContracts.length" class="text-muted text-center mt-3">
            No vesting contracts found.
          </p>
        </div>
      </div>
    </template>

    <!-- 🔹 Contract Details Modal -->
    <div v-if="selectedContract" class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Vesting Contract Details</h5>
            <button type="button" class="btn-close" @click="closeModal"><i class="bi bi-x"></i></button>
          </div>
          <div class="modal-body">
            <div class="row g-3">
              <!-- Asset Information -->
              <div class="col-md-6">
                <div class="info-card">
                  <label class="info-label">Asset Name</label>
                  <p class="info-value">{{ selectedContract.asset_name }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-card">
                  <label class="info-label">Asset Type</label>
                  <p class="info-value">{{ selectedContract.asset_type }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-card">
                  <label class="info-label">Total Value</label>
                  <p class="info-value">${{ selectedContract.asset_value.toLocaleString() }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-card">
                  <label class="info-label">Status</label>
                  <p class="info-value">
                    <span :class="`status-badge ${getStatusClass(selectedContract.status)}`">
                      {{ selectedContract.status }}
                    </span>
                  </p>
                </div>
              </div>

              <!-- Vesting Chart -->
              <div class="col-12">
                <h6 class="section-title">Vesting Schedule</h6>
                <div class="info-card">
                  <div v-if="isLoadingSchedule" class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </div>
                  <div v-else-if="scheduleError" class="alert alert-danger">
                    {{ scheduleError }}
                  </div>
                  <div v-else class="vesting-chart-container">
                    <canvas ref="vestingChart"></canvas>
                    <div class="chart-legend mt-3">
                      <div class="d-flex justify-content-between align-items-center">
                        <div>
                          <span class="legend-item">
                            <span class="legend-color" style="background: #4CAF50"></span>
                            Vested Amount
                          </span>
                          <span class="legend-item ms-3">
                            <span class="legend-color" style="background: #2196F3"></span>
                            Vested Percentage
                          </span>
                        </div>
                        <div class="text-muted">
                          <small>Last updated: {{ formatDate(new Date()) }}</small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Vesting Schedule Details -->
              <div class="col-12">
                <div class="info-card">
                  <div class="row g-3">
                    <div class="col-md-4">
                      <label class="info-label">Start Date</label>
                      <p class="info-value">{{ formatDate(selectedContract.vesting_schedule.start_date) }}</p>
                    </div>
                    <div class="col-md-4">
                      <label class="info-label">End Date</label>
                      <p class="info-value">{{ formatDate(selectedContract.vesting_schedule.end_date) }}</p>
                    </div>
                    <div class="col-md-4">
                      <label class="info-label">Cliff Period</label>
                      <p class="info-value">{{ selectedContract.vesting_schedule.cliff_period }} days</p>
                    </div>
                    <div class="col-md-4">
                      <label class="info-label">Release Frequency</label>
                      <p class="info-value">{{ selectedContract.vesting_schedule.release_frequency }}</p>
                    </div>
                    <div class="col-md-4">
                      <label class="info-label">Contract Address</label>
                      <p class="info-value">
                        <a :href="getEtherscanLink(selectedContract.contract_address)"
                           target="_blank"
                           class="hash-link">
                          {{ shortenHash(selectedContract.contract_address) }}
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Beneficiaries -->
              <div class="col-12">
                <h6 class="section-title">Beneficiaries</h6>
                <div class="table-responsive">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Percentage</th>
                        <th>Vested Amount</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="beneficiary in selectedContract.beneficiaries" :key="beneficiary.beneficiary_id">
                        <td>{{ beneficiary.name }}</td>
                        <td>{{ beneficiary.percentage }}%</td>
                        <td>${{ calculateBeneficiaryVestedAmount(selectedContract, beneficiary) }}</td>
                        <td>
                          <span :class="`status-badge ${getBeneficiaryStatusClass(selectedContract, beneficiary)}`">
                            {{ getBeneficiaryStatus(selectedContract, beneficiary) }}
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="selectedContract" class="modal-backdrop fade show"></div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from "vue";
import { useAuthStore } from "@/stores/auth";
import apiClient from "@/services/apiClient";
import { useToast } from "vue-toastification";
import Chart from 'chart.js/auto';

export default {
  setup() {
    const toast = useToast();
    const authStore = useAuthStore();
    const vestingContracts = ref([]);
    const totalContracts = ref(0);
    const activeVestings = ref(0);
    const totalValueLocked = ref(0);
    const isLoading = ref(false);
    const error = ref(null);
    const selectedContract = ref(null);
    const vestingChart = ref(null);
    const chartInstance = ref(null);
    const isLoadingSchedule = ref(false);
    const scheduleError = ref(null);
    const scheduleData = ref(null);

    const isManager = computed(() => authStore.user?.role === "manager");

    const fetchVestingContracts = async () => {
      isLoading.value = true;
      error.value = null;
      try {
        const response = await apiClient.get('/vesting/user');
        vestingContracts.value = response.data.vestingRecords;
        updateStats();
      } catch (err) {
        error.value = "Failed to fetch vesting records.";
        console.error("Error fetching vesting records:", err);
        toast.error(error.value);
      } finally {
        isLoading.value = false;
      }
    };

    const updateStats = () => {
      totalContracts.value = vestingContracts.value.length;
      activeVestings.value = vestingContracts.value.filter(c => c.status === 'active').length;
      totalValueLocked.value = vestingContracts.value.reduce((sum, c) => sum + parseFloat(c.asset_value), 0);
    };

    const formatDate = (dateString) => {
      if (!dateString) return 'N/A';
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid Date';
      }
    };

    const getStatusClass = (status) => ({
      active: "text-success",
      pending: "text-warning",
      completed: "text-info",
      cancelled: "text-danger"
    }[status] || "text-muted");

    const viewContractDetails = async (contract) => {
      selectedContract.value = contract;
      await fetchVestingSchedule(contract.id);
    };

    const closeModal = () => {
      selectedContract.value = null;
    };

    const approveContract = async (contractId) => {
      try {
        await apiClient.post(`/vesting/approve/${contractId}`);
        toast.success("Contract approved successfully!");
        fetchVestingContracts();
      } catch (err) {
        console.error("Error approving contract:", err);
        toast.error("Failed to approve contract.");
      }
    };

    const getEtherscanLink = (address) => {
      return `https://etherscan.io/address/${address}`;
    };

    const shortenHash = (hash) => {
      if (!hash) return '';
      return `${hash.substring(0, 6)}...${hash.substring(hash.length - 4)}`;
    };

    const calculateBeneficiaryVestedAmount = (contract, beneficiary) => {
      const progress = calculateVestingProgress(contract);
      return (contract.asset_value * beneficiary.percentage / 100) * (progress / 100);
    };

    const calculateVestingProgress = (contract) => {
      const startDate = new Date(contract.vesting_schedule.start_date);
      const endDate = new Date(contract.vesting_schedule.end_date);
      const now = new Date();
      const totalDuration = endDate - startDate;
      const elapsed = now - startDate;
      return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
    };

    const getBeneficiaryStatus = (contract, beneficiary) => {
      const progress = calculateVestingProgress(contract);
      if (progress === 0) return 'Not Started';
      if (progress === 100) return 'Completed';
      return 'In Progress';
    };

    const getBeneficiaryStatusClass = (contract, beneficiary) => {
      const status = getBeneficiaryStatus(contract, beneficiary);
      return {
        'Not Started': 'text-secondary',
        'In Progress': 'text-warning',
        'Completed': 'text-success'
      }[status] || 'text-muted';
    };

    const fetchVestingSchedule = async (contractId) => {
      isLoadingSchedule.value = true;
      scheduleError.value = null;
      try {
        const response = await apiClient.get(`/vesting/${contractId}/schedule`);
        scheduleData.value = response.data;
        updateChart();
      } catch (err) {
        scheduleError.value = "Failed to fetch vesting schedule.";
        console.error("Error fetching vesting schedule:", err);
        toast.error(scheduleError.value);
      } finally {
        isLoadingSchedule.value = false;
      }
    };

    const updateChart = () => {
      if (!scheduleData.value || !vestingChart.value) return;

      if (chartInstance.value) {
        chartInstance.value.destroy();
      }

      // Use vesting schedule from the backend response
      const vestingSchedule = scheduleData.value.vesting.schedule;

      // Use vesting start date from the backend response
      const startDate = new Date(scheduleData.value.vesting.start_date);
      const durationDays = vestingSchedule.duration; // assuming duration is in days
      const endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000);

      const currentProgress = vestingSchedule.current_progress;
      const currentVestedPercentage = currentProgress.vested_percentage;
      const assetVal = scheduleData.value.asset.value;

      const currentVestedAmount = assetVal * (currentVestedPercentage / 100);

      // Create chart data points: start, current, and end
      const chartDataPoints = [
        { date: startDate, vestedAmount: 0, vestedPercentage: 0 },
        { date: new Date(), vestedAmount: currentVestedAmount, vestedPercentage: currentVestedPercentage },
        { date: endDate, vestedAmount: assetVal, vestedPercentage: 100 }
      ];

      const ctx = vestingChart.value.getContext('2d');

      chartInstance.value = new Chart(ctx, {
        type: 'line',
        data: {
          labels: chartDataPoints.map(point => formatDate(point.date)),
          datasets: [
            {
              label: 'Vested Amount ($)',
              data: chartDataPoints.map(point => point.vestedAmount),
              borderColor: '#4CAF50',
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
              fill: true,
              yAxisID: 'y'
            },
            {
              label: 'Vested Percentage (%)',
              data: chartDataPoints.map(point => point.vestedPercentage),
              borderColor: '#2196F3',
              backgroundColor: 'rgba(33, 150, 243, 0.1)',
              fill: true,
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          responsive: true,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          scales: {
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              title: {
                display: true,
                text: 'Vested Amount ($)'
              }
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              title: {
                display: true,
                text: 'Vested Percentage (%)'
              },
              grid: {
                drawOnChartArea: false
              }
            }
          },
          plugins: {
            tooltip: {
              callbacks: {
                label: function(context) {
                  let label = context.dataset.label || '';
                  if (label) {
                    label += ': ';
                  }
                  if (context.datasetIndex === 0) {
                    label += '$' + context.parsed.y.toLocaleString();
                  } else {
                    label += context.parsed.y.toFixed(1) + '%';
                  }
                  return label;
                }
              }
            }
          }
        }
      });
    };

    watch(selectedContract, (newContract) => {
      if (newContract) {
        fetchVestingSchedule(newContract.id);
      }
    });

    onMounted(() => {
      fetchVestingContracts();
    });
    const listBeneficiaries = (record) => {
  if (!record || !record.length) return 'N/A';
  return record.map(record => record.name).join(', ');
};
    return {
      vestingContracts,
      totalContracts,
      activeVestings,
      totalValueLocked,
      isManager,
      isLoading,
      error,
      selectedContract,
      formatDate,
      getStatusClass,
      viewContractDetails,
      closeModal,
      approveContract,
      fetchVestingContracts,
      getEtherscanLink,
      shortenHash,
      calculateBeneficiaryVestedAmount,
      getBeneficiaryStatus,
      getBeneficiaryStatusClass,
      vestingChart,
      isLoadingSchedule,
      scheduleError,
      scheduleData,
      listBeneficiaries,
    };
  },
};
</script>

<style scoped>
.vesting-stat {
  text-align: center;
  padding: 1rem;
  background: rgba(var(--bs-light-rgb), 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(var(--bs-light-rgb), 0.2);
    transform: translateY(-2px);
  }

  h6 {
    color: var(--bs-muted);
    margin-bottom: 0.5rem;
  }

  .h3 {
    margin: 0;
    color: var(--bs-primary);
  }
}

.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: bold;
}

.status-badge.text-success {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.status-badge.text-warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.status-badge.text-info {
  background: rgba(23, 162, 184, 0.2);
  color: #17a2b8;
}

.status-badge.text-danger {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

code {
  background: rgba(var(--bs-light-rgb), 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  color: var(--bs-primary);
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1040;
}

.info-card {
  background: rgba(var(--bs-primary-rgb), 0.05);
  border-radius: 8px;
  padding: 1rem;
}

.info-label {
  font-size: 0.875rem;
  color: var(--bs-muted);
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--bs-primary);
  margin-bottom: 1rem;
}

.hash-link {
  color: var(--bs-primary);
  text-decoration: none;
  font-family: monospace;
  font-size: 0.875rem;
}

.hash-link:hover {
  text-decoration: underline;
}

.vesting-chart-container {
  position: relative;
  height: auto;
  width: 100%;
}

.chart-legend {
  font-size: 0.875rem;
}

.legend-item {
  display: inline-flex;
  align-items: center;
  margin-right: 1rem;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: 0.5rem;
  border-radius: 2px;
}
</style>
