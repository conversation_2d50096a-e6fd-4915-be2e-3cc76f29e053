<template>
    <div class="login-container d-flex flex-column align-items-center justify-content-center w-100">

<!-- Top Header Section (Logo + Theme Toggle + Home) -->
<header class="non-logged-in-header-ele">
  <a href="/" class="logo-container">
    <img src="@/assets/images/alchemy finance logo.svg" alt="Archimedes Finance Logo" class="login-logo" />
    <span class="project-name">Archimedes Finance</span>
  </a>

  <div class="header-controls">
    <!-- Theme Toggle -->
    <button @click="toggleTheme" class="btn btn-outline-secondary theme-toggle">
      <i :class="theme === 'light' ? 'bi bi-moon-stars' : 'bi bi-sun'"></i>
    </button>

    <!-- Home Link -->
    <RouterLink to="/" class="btn btn-outline-primary">Home</RouterLink>
  </div>
</header>



  <!-- <div class="login-container"> -->
    <div class="container">
      <div class="row d-flex justify-content-center w-100">
        <!-- Login Card -->
        <div class="login-card">
          <!-- Header -->
          <div class="login-header text-center mb-4">
            <h3 class="fw-bold">Welcome Back</h3>
            <p class="text-muted">Sign in to continue managing your investments</p>
          </div>

          <!-- Error Alert -->
          <transition name="fade">
            <div v-if="errorMessage" class="alert alert-danger d-flex align-items-center" role="alert">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              <div>
                {{ errorMessage }}
                <button v-if="errorDetails" class="btn btn-link p-0 ms-2" @click="showDetails = !showDetails">
                  {{ showDetails ? 'Hide details' : 'Show details' }}
                </button>
                <pre v-if="showDetails" class="mt-2 p-2 rounded text-dark">{{ errorDetails }}</pre>
              </div>
            </div>
          </transition>

          <!-- Login Form -->
          <form @submit.prevent="login" class="login-form">
            <!-- Email Input -->
            <div class="form-floating mb-3">
              <input
                id="email"
                v-model="email"
                type="email"
                class="form-control"
                placeholder="Enter your email"
                required
                autocomplete="email"
              />
              <label for="email">Email Address</label>
            </div>

            <!-- Password Input (only show if not using magic link) -->
            <div v-if="!isMagicLinkMode" class="form-floating mb-3">
              <div class="input-group">
                <input
                  id="password"
                  v-model="password"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-control"
                  placeholder=" "
                  required
                  autocomplete="current-password"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  @click="showPassword = !showPassword"
                  aria-label="Toggle password visibility"
                >
                  <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                </button>
              </div>
              <label for="password">Password</label>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div v-if="!isMagicLinkMode" class="d-flex align-items-center mb-3">
              <div class="form-check">
                <input
                  type="checkbox"
                  id="rememberMe"
                  class="form-check-input"
                  v-model="rememberMe"
                />
                <label for="rememberMe" class="form-check-label">Trust this device for 24 hours</label>
              </div>
            </div>

            <div v-if="!isMagicLinkMode" class="d-flex align-items-center mb-3">
              <button
                class="btn btn-link text-decoration-none p-0"
                @click="goToResetPassword"
                type="button"
              >
                Forgot Password?
              </button>
            </div>

            <!-- Robot Protection (only show if not using magic link) -->
            <RobotProtection v-if="!isMagicLinkMode" @validation="handleRobotValidation" />

            <!-- Submit Button -->
            <button
              type="submit"
              class="btn btn-primary w-100 login-btn"
              :disabled="isLoading || (!isRobotValid && !isMagicLinkMode)"
            >
              <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
              {{ isMagicLinkMode ? 'Send Magic Link' : 'Sign In' }}
            </button>

            <!-- Magic Link Login -->
            <div class="text-center mt-3">
            <!-- Magic Link Info -->
            <div v-if="isMagicLinkMode" class="alert alert-info mt-3">
              <p class="mb-1">We'll send you a magic link to sign in instantly.</p>
              <p class="mb-1">Please check your spam folder if you don't see the email.</p>
              <p class="mb-0">You can request a new link every 5 minutes.</p>
            </div>
              <button
                type="button"
                class="btn btn-link text-decoration-none"
                @click="toggleMagicLinkMode"
                :disabled="isLoading || cooldownTimer > 0"
              >
                {{ isMagicLinkMode ? 'Sign in with Password' : 'Sign in with Magic Link' }}
                <span v-if="cooldownTimer > 0">({{ cooldownTimer }}s)</span>
              </button>
            </div>

          </form>

          <!-- Register CTA -->
          <div class="text-center mt-4">
            <p class="mb-1">Don't have an account?</p>
            <button
              class="btn btn-outline-primary w-100"
              @click="goToRegister"
              type="button"
            >
              Create Account
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthStore } from "@/stores/auth";
import { ref, onMounted } from "vue";
import RobotProtection from '@/components/RobotProtection.vue';
import apiClient from '@/services/apiClient';
import { useToast } from 'vue-toastification';

export default {
  components: {
    RobotProtection
  },
  setup() {
    const theme = ref(localStorage.getItem("theme") || "light");
    const toast = useToast();

    const toggleTheme = () => {
      theme.value = theme.value === "dark" ? "light" : "dark";
      document.documentElement.setAttribute("data-bs-theme", theme.value);
      localStorage.setItem("theme", theme.value);
    };

    onMounted(() => {
      document.documentElement.setAttribute("data-bs-theme", theme.value);
    });

    return { theme, toggleTheme, toast };
  },
  data() {
    return {
      email: localStorage.getItem('email') || "",
      password: "",
      isLoading: false,
      errorMessage: null,
      errorDetails: null,
      showDetails: false,
      showPassword: false,
      rememberMe: false,
      isRobotValid: false,
      isMagicLinkMode: false,
      cooldownTimer: 0
    };
  },
  methods: {
    async login() {
      if (this.isMagicLinkMode) {
        await this.requestMagicLink();
        return;
      }

      if (!this.isRobotValid) {
        this.errorMessage = 'Please complete the security check';
        return;
      }

      const authStore = useAuthStore();
      this.isLoading = true;
      this.errorMessage = null;
      this.errorDetails = null;
      this.showDetails = false;

      try {
        // Pass the rememberMe flag to the login method
        await authStore.login(this.email, this.password, this.rememberMe);

        // Store email for convenience if rememberMe is checked
        if (this.rememberMe) {
          localStorage.setItem('email', this.email);
        } else {
          localStorage.removeItem('email');
        }

        this.$router.push("/dashboard");
      } catch (error) {
        this.errorMessage = error.message;
        this.errorDetails = `Status: ${error.response?.status || "N/A"}\nMessage: ${error.message}`;
        console.error("Login error:", error.message);
      } finally {
        this.isLoading = false;
      }
    },

    async requestMagicLink() {
      if (!this.email) {
        this.errorMessage = 'Please enter your email address';
        return;
      }

      this.isLoading = true;
      this.errorMessage = null;

      try {
        const res = await apiClient.post('/auth/magic-link/request', { email: this.email });

        if (res.ok && res.data.success) {
          this.toast.success('Magic link sent! Please check your email.');
          if (this.rememberMe) {
            localStorage.setItem('email', this.email);
          }

          // Start cooldown timer
          this.cooldownTimer = res.data.cooldown;
          this.startCooldownTimer();
        } else {
          this.errorMessage = res.errors[0] || 'Failed to send magic link';
          if (res.data?.timeLeft) {
            this.cooldownTimer = res.data.timeLeft;
            this.startCooldownTimer();
          }
        }
      } catch (error) {
        this.errorMessage = error.response?.data?.message || 'Failed to send magic link';
        if (error.response?.data?.timeLeft) {
          this.cooldownTimer = error.response.data.timeLeft;
          this.startCooldownTimer();
        }
        console.error('Magic link request error:', error);
      } finally {
        this.isLoading = false;
      }
    },

    startCooldownTimer() {
      const timer = setInterval(() => {
        if (this.cooldownTimer > 0) {
          this.cooldownTimer--;
        } else {
          clearInterval(timer);
        }
      }, 1000);
    },

    toggleMagicLinkMode() {
      this.isMagicLinkMode = !this.isMagicLinkMode;
      this.errorMessage = null;

      // If switching to magic link mode and email is already entered, submit immediately
      if (this.isMagicLinkMode && this.email) {
        this.requestMagicLink();
      }
    },

    goToRegister() {
      this.$router.push("/auth/register");
    },
    goToResetPassword() {
      this.$router.push("/auth/reset-password");
    },
    handleRobotValidation(isValid) {
      this.isRobotValid = isValid;
    }
  }
};
</script>

<style scoped lang="scss">
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 6rem 0 2rem;
  background-color: var(--bs-body-bg);
  position: relative;
  .align-items-center {
  justify-content: center !important;
}
  @media (max-width: 768px) {
    padding-top: 5rem;
  }
}

.login-card {
  width: 100%;
  max-width: 420px;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(var(--bs-dark-rgb), 0.08);
  transition: all 0.3s ease;
  margin: 0 auto;

  @media (max-width: 576px) {
    padding: 2rem 1.5rem;
    border-radius: 12px;
  }
}

.login-logo {
  height: 40px;
  width: auto;
  transition: all 0.3s ease;
}

.login-header {
  h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--bs-body-color);
    margin-bottom: 0.75rem;
    letter-spacing: -0.01em;
  }

  p {
    font-size: 1rem;
    color: var(--bs-body-color);
    opacity: 0.7;
  }
}

.login-form {
  .form-floating {
    margin-bottom: 1.25rem;

    .form-control {
      border-radius: 10px;
      padding: 1rem 0.75rem;
      height: auto;
      min-height: 3.5rem;
      background-color: var(--bs-body-bg);
      border: 1px solid var(--bs-border-color);
      color: var(--bs-body-color);
      transition: all 0.2s ease;

      &:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
      }

      &::placeholder {
        color: var(--bs-body-color);
        opacity: 0.5;
      }
    }

    .input-group {
      .form-control {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      .btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        background-color: var(--bs-body-bg);
        border: 1px solid var(--bs-border-color);
        border-left: none;
        color: var(--bs-body-color);

        &:hover {
          background-color: rgba(var(--bs-primary-rgb), 0.05);
          color: var(--bs-primary);
        }
      }
    }

    label {
      color: var(--bs-body-color);
      opacity: 0.7;
    }
  }

  .form-check {
    .form-check-input {
      border-color: var(--bs-border-color);
      background-color: var(--bs-body-bg);

      &:checked {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
      }
    }

    .form-check-label {
      color: var(--bs-body-color);
      opacity: 0.8;
      font-size: 0.95rem;
    }
  }

  .btn-link {
    color: var(--bs-primary);
    font-weight: 500;
    font-size: 0.95rem;

    &:hover {
      text-decoration: underline !important;
    }
  }

  .login-btn {
    height: 3.5rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s ease;
    margin-top: 0.5rem;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(var(--bs-primary-rgb), 0.2);
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
}

// Alert styling
.alert {
  border-radius: 10px;
  padding: 1rem 1.25rem;

  &.alert-danger {
    background-color: rgba(var(--bs-danger-rgb), 0.1);
    border: 1px solid rgba(var(--bs-danger-rgb), 0.2);
    color: var(--bs-danger);
  }

  &.alert-info {
    background-color: rgba(var(--bs-info-rgb), 0.1);
    border: 1px solid rgba(var(--bs-info-rgb), 0.2);
    color: var(--bs-body-color);
    flex-direction: column;
    gap: 0.15rem;
    padding: 1.25rem;

    p {
      opacity: 0.8;
      font-size: 0.9rem;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Register CTA
.mt-4 {
  p {
    color: var(--bs-body-color);
    opacity: 0.7;
    font-size: 0.95rem;
  }

  .btn-outline-primary {
    height: 3.25rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(var(--bs-primary-rgb), 0.15);
    }
  }
}

// Animations
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Fix for floating label on auto-filled password input */
:deep(.form-floating input:-webkit-autofill ~ label) {
  transform: translateY(-0.5rem) scale(0.85);
  transition: transform 0.2s ease-out;
}

// Header styling
.non-logged-in-header-ele {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--bs-card2-bg);
  border-bottom: 1px solid var(--bs-border-color);
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(var(--bs-dark-rgb), 0.05);

  @media (max-width: 768px) {
    padding: 0.75rem 1rem;
  }

  .logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;

    .project-name {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--bs-primary);

      @media (max-width: 576px) {
        display: none;
      }
    }
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .theme-toggle {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      i {
        font-size: 1.1rem;
      }
    }
  }
}

</style>
