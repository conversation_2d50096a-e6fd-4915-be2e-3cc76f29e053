<template>
  <div class="kyc-verification">
    <div class="container">
      <!-- Header Section -->
      <div class="dashboard-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h1 class="header-title">Identity Verification</h1>
            <p class="header-subtitle">Complete your KYC verification to access advanced platform features</p>
          </div>
          <button class="btn btn-outline-primary refresh-btn" @click="fetchKycStatus">
            <i class="bi bi-arrow-clockwise"></i> Refresh Status
          </button>
        </div>
      </div>

      <!-- Success State -->
      <div v-if="kycStatus?.status === 'verified'" class="dashboard-card success-state mb-4">
        <div class="card-body text-center py-5">
          <div class="success-icon mb-4">
            <i class="bi bi-check-circle-fill"></i>
          </div>
          <h3 class="h4 mb-3">Verification Complete</h3>
          <p class="text-muted mb-4">Your identity has been successfully verified. You now have access to all platform features.</p>
          <div class="verification-details">
            <p class="text-muted mb-2">
              <i class="bi bi-calendar-check me-2"></i>
              Verified on: {{ new Date(kycStatus.verified_at).toLocaleDateString() }}
            </p>
            <p class="text-muted">
              <i class="bi bi-shield-check me-2"></i>
              Your data is secured with enterprise-grade encryption
            </p>
          </div>
        </div>
      </div>

      <!-- Pending State -->
      <div v-else-if="kycStatus?.status === 'pending'" class="dashboard-card pending-state mb-4">
        <div class="card-body text-center py-5">
          <div class="pending-icon mb-4">
            <i class="bi bi-clock-fill"></i>
          </div>
          <h3 class="h4 mb-3">Verification in Progress</h3>
          <p class="text-muted mb-4">Your verification documents are being reviewed. This process typically takes 24-48 hours.</p>
          <div class="verification-details">
            <p class="text-muted mb-2">
              <i class="bi bi-send-fill me-2"></i>
              Submitted on: {{ new Date(kycStatus.submitted_at).toLocaleDateString() }}
            </p>
            <p class="text-muted">
              <i class="bi bi-info-circle-fill me-2"></i>
              You will receive a notification once your verification is complete
            </p>
          </div>
        </div>
      </div>

      <!-- Failed State -->
      <div v-else-if="kycStatus?.status === 'failed'" class="dashboard-card failed-state mb-4">
        <div class="card-body text-center py-5">
          <div class="failed-icon mb-4">
            <i class="bi bi-exclamation-circle-fill"></i>
          </div>
          <h3 class="h4 mb-3">Verification Failed</h3>
          <p class="text-muted mb-4">We were unable to verify your identity. Please ensure your documents are clear and valid.</p>
          <div class="verification-details mb-4">
            <p class="text-muted mb-2">
              <i class="bi bi-calendar-x me-2"></i>
              Last attempt: {{ new Date(kycStatus.submitted_at).toLocaleDateString() }}
            </p>
            <p class="text-danger">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              {{ kycStatus.rejection_reason || 'Please try again with clearer documents' }}
            </p>
          </div>
          <button class="btn btn-primary" @click="resetVerification">
            <i class="bi bi-arrow-repeat me-2"></i> Try Again
          </button>
        </div>
      </div>

      <!-- Verification Form -->
      <div v-if="showVerificationForm" class="dashboard-card">
        <div class="card-body">
          <!-- Progress Steps -->
          <div class="progress-steps mb-5">
            <div class="d-flex justify-content-between position-relative">
              <div class="progress-line"></div>
              <div v-for="(step, index) in steps" :key="index"
                   class="step-item"
                   :class="{'active': currentStep >= index, 'completed': currentStep > index}">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-label">{{ step.label }}</div>
              </div>
            </div>
          </div>

          <form @submit.prevent="submitKyc" class="space-y-4">
            <!-- Step 1: Passport Upload -->
            <div v-if="currentStep === 0" class="process-card">
              <div class="process-icon">
                <i class="bi bi-passport"></i>
                <span class="step-number">1</span>
              </div>
              <h3 class="h5 mb-3">Upload Passport</h3>
              <p class="text-muted mb-4">Upload a clear photo of your passport's main page. Ensure all text is clearly visible.</p>

              <div
                class="upload-area"
                :class="{'has-file': passportPreview}"
                @dragover.prevent
                @drop.prevent="handleDrop"
              >
                <input
                  type="file"
                  ref="passportInput"
                  accept="image/*"
                  class="d-none"
                  @change="handlePassportUpload"
                />
                <div v-if="!passportPreview" class="upload-content">
                  <div class="upload-icon">
                    <i class="bi bi-passport"></i>
                  </div>
                  <div class="upload-text">
                    <p class="mb-2">Drag and drop your passport image here or</p>
                    <button
                      type="button"
                      @click="$refs.passportInput.click()"
                      class="btn btn-primary"
                    >
                      <i class="bi bi-upload me-2"></i> Choose File
                    </button>
                    <p class="text-muted small mt-2">Maximum file size: 5MB</p>
                  </div>
                </div>
                <div v-else class="preview-content">
                  <img
                    :src="passportPreview"
                    class="img-fluid rounded"
                    alt="Passport preview"
                  />
                  <button
                    type="button"
                    @click="removePassport"
                    class="btn btn-danger mt-3"
                  >
                    <i class="bi bi-trash me-2"></i> Remove
                  </button>
                </div>
              </div>

              <div class="d-flex justify-content-end mt-4">
                <button
                  type="button"
                  @click="nextStep"
                  :disabled="!passportPreview"
                  class="btn btn-primary"
                >
                  Next Step <i class="bi bi-arrow-right ms-2"></i>
                </button>
              </div>
            </div>

            <!-- Step 2: Selfie Verification -->
            <div v-if="currentStep === 1" class="process-card">
              <div class="process-icon">
                <i class="bi bi-person-circle"></i>
                <span class="step-number">2</span>
              </div>
              <h3 class="h5 mb-3">Take a Selfie</h3>
              <p class="text-muted mb-4">Please ensure your face is clearly visible and well-lit. Remove any glasses or face coverings.</p>

              <div class="camera-container">
                <video
                  v-if="!capturedSelfie"
                  ref="video"
                  class="camera-preview"
                  autoplay
                  playsinline
                ></video>
                <img
                  v-else
                  :src="capturedSelfie"
                  class="camera-preview"
                  alt="Captured selfie"
                />
              </div>

              <div class="camera-controls">
                <button
                  v-if="!capturedSelfie"
                  type="button"
                  @click="startCamera"
                  class="btn btn-outline-primary"
                >
                  <i class="bi bi-camera me-2"></i> Start Camera
                </button>
                <button
                  v-if="!capturedSelfie"
                  type="button"
                  @click="captureSelfie"
                  class="btn btn-success"
                >
                  <i class="bi bi-camera-fill me-2"></i> Capture Photo
                </button>
                <button
                  v-if="capturedSelfie"
                  type="button"
                  @click="retakeSelfie"
                  class="btn btn-danger"
                >
                  <i class="bi bi-arrow-counterclockwise me-2"></i> Retake
                </button>
              </div>

              <div class="d-flex justify-content-between mt-4">
                <button
                  type="button"
                  @click="previousStep"
                  class="btn btn-outline-secondary"
                >
                  <i class="bi bi-arrow-left me-2"></i> Previous
                </button>
                <button
                  type="button"
                  @click="nextStep"
                  :disabled="!capturedSelfie"
                  class="btn btn-primary"
                >
                  Next Step <i class="bi bi-arrow-right ms-2"></i>
                </button>
              </div>
            </div>

            <!-- Step 3: Review -->
            <div v-if="currentStep === 2" class="process-card">
              <div class="process-icon">
                <i class="bi bi-check-circle"></i>
                <span class="step-number">3</span>
              </div>
              <h3 class="h5 mb-3">Review Your Documents</h3>
              <p class="text-muted mb-4">Please review your uploaded documents before submission. Ensure all information is clear and accurate.</p>

              <div class="review-container">
                <div class="row g-4">
                  <div class="col-md-6">
                    <div class="review-card">
                      <div class="card-body">
                        <h5 class="card-title">
                          <i class="bi bi-passport me-2"></i> Passport
                        </h5>
                        <div class="review-image">
                          <img
                            :src="passportPreview"
                            class="img-fluid rounded"
                            alt="Passport preview"
                          />
                        </div>
                        <button
                          type="button"
                          @click="goToStep(0)"
                          class="btn btn-outline-primary w-100 mt-3"
                        >
                          <i class="bi bi-pencil-square me-2"></i> Edit Passport
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="review-card">
                      <div class="card-body">
                        <h5 class="card-title">
                          <i class="bi bi-person-circle me-2"></i> Selfie
                        </h5>
                        <div class="review-image">
                          <img
                            :src="capturedSelfie"
                            class="img-fluid rounded"
                            alt="Selfie preview"
                          />
                        </div>
                        <button
                          type="button"
                          @click="goToStep(1)"
                          class="btn btn-outline-primary w-100 mt-3"
                        >
                          <i class="bi bi-pencil-square me-2"></i> Edit Selfie
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex justify-content-between mt-4">
                <button
                  type="button"
                  @click="previousStep"
                  class="btn btn-outline-secondary"
                >
                  <i class="bi bi-arrow-left me-2"></i> Previous
                </button>
                <button
                  type="submit"
                  :disabled="loading"
                  class="btn btn-success"
                >
                  <i v-if="!loading" class="bi bi-check-circle me-2"></i>
                  <i v-else class="bi bi-arrow-repeat me-2"></i>
                  {{ loading ? 'Submitting...' : 'Submit Verification' }}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>

      <!-- Help Section -->
      <div class="dashboard-card mt-4">
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <i class="bi bi-headset text-primary me-3"></i>
            <h3 class="h5 mb-0">Need Help?</h3>
          </div>
          <p class="text-muted mb-4">If you encounter any issues during the verification process, our support team is here to help.</p>
          <button class="btn btn-outline-primary">
            <i v-if="!isReportModalLoading" class="bi bi-chat-dots me-2"></i>
            <i v-else class="bi bi-arrow-repeat me-2"></i>
            Contact Support
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { useToast } from 'vue-toastification';
import apiClient from '@/services/apiClient';

export default {
  name: 'KycVerification',
  setup() {
    const toast = useToast();
    const video = ref(null);
    const stream = ref(null);
    const capturedSelfie = ref(null);
    const passportPreview = ref(null);
    const passportFile = ref(null);
    const kycStatus = ref(null);
    const loading = ref(false);
    const currentStep = ref(0);
    const showVerificationForm = ref(false);

    const steps = [
      { label: 'Passport', icon: 'fa-passport' },
      { label: 'Selfie', icon: 'fa-user-circle' },
      { label: 'Review', icon: 'fa-check-circle' }
    ];

    const canSubmit = computed(() => {
      return capturedSelfie.value && passportFile.value && !loading.value;
    });

    const nextStep = () => {
      if (currentStep.value < steps.length - 1) {
        currentStep.value++;
      }
    };

    const previousStep = () => {
      if (currentStep.value > 0) {
        currentStep.value--;
      }
    };

    const goToStep = (step) => {
      currentStep.value = step;
    };

    const resetVerification = () => {
      kycStatus.value = null;
      currentStep.value = 0;
      capturedSelfie.value = null;
      passportPreview.value = null;
      passportFile.value = null;
      showVerificationForm.value = true;
    };

    const clearForm = () => {
      currentStep.value = 0;
      capturedSelfie.value = null;
      passportPreview.value = null;
      passportFile.value = null;
      if (stream.value) {
        stream.value.getTracks().forEach(track => track.stop());
        stream.value = null;
      }
      if (video.value) {
        video.value.srcObject = null;
      }
    };

    const getStatusBadgeClass = (status) => {
      switch (status) {
        case 'verified':
          return 'text-success';
        case 'pending':
          return 'text-warning';
        case 'failed':
          return 'text-danger';
        case 'not_submitted':
          return 'text-muted';
        default:
          return 'text-muted';
      }
    };

    const getStatusIcon = (status) => {
      switch (status) {
        case 'verified':
          return 'fas fa-check-circle';
        case 'pending':
          return 'fas fa-clock';
        case 'failed':
          return 'fas fa-exclamation-circle';
        case 'not_submitted':
          return 'fas fa-user-circle';
        default:
          return 'fas fa-question-circle';
      }
    };

    const getStatusText = (status) => {
      switch (status) {
        case 'verified':
          return 'Identity Verified';
        case 'pending':
          return 'Verification in Progress';
        case 'failed':
          return 'Verification Failed';
        case 'not_submitted':
          return 'Verification Not Started';
        default:
          return 'Unknown Status';
      }
    };

    const handleDrop = (event) => {
      const file = event.dataTransfer.files[0];
      if (file && file.type.startsWith('image/')) {
        handleFile(file);
      } else {
        toast.error('Please upload an image file');
      }
    };

    const handleFile = (file) => {
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }
      passportFile.value = file;
      const reader = new FileReader();
      reader.onload = (e) => {
        passportPreview.value = e.target.result;
      };
      reader.readAsDataURL(file);
    };

    const startCamera = async () => {
      try {
        stream.value = await navigator.mediaDevices.getUserMedia({ video: true });
        video.value.srcObject = stream.value;
      } catch (error) {
        console.error('Camera access error:', error);
        toast.error('Failed to access camera. Please ensure camera permissions are granted.');
      }
    };

    const captureSelfie = () => {
      const canvas = document.createElement('canvas');
      canvas.width = video.value.videoWidth;
      canvas.height = video.value.videoHeight;
      canvas.getContext('2d').drawImage(video.value, 0, 0);
      capturedSelfie.value = canvas.toDataURL('image/jpeg');
      stopCamera();
    };

    const retakeSelfie = () => {
      capturedSelfie.value = null;
      startCamera();
    };

    const stopCamera = () => {
      if (stream.value) {
        stream.value.getTracks().forEach(track => track.stop());
        stream.value = null;
      }
    };

    const handlePassportUpload = (event) => {
      const file = event.target.files[0];
      if (file) {
        if (file.size > 5 * 1024 * 1024) {
          toast.error('File size must be less than 5MB');
          return;
        }
        passportFile.value = file;
        const reader = new FileReader();
        reader.onload = (e) => {
          passportPreview.value = e.target.result;
        };
        reader.readAsDataURL(file);
      }
    };

    const removePassport = () => {
      passportFile.value = null;
      passportPreview.value = null;
      if (video.value) {
        video.value.srcObject = null;
      }
    };

    const fetchKycStatus = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          toast.error('Please log in to access KYC verification');
          return;
        }

        const res = await apiClient.get('/kyc/status');
        if (res.ok) {
          kycStatus.value = res.data.data || { status: 'not_submitted' };
        } else {
          res.errors.forEach(e => console.error(e));
        }
        // Hide form if status is failed
        if (kycStatus.value.status === 'failed') {
          showVerificationForm.value = false;
        }
      } catch (error) {
        console.error('Error fetching KYC status:', error);
        if (error.response?.status === 401) {
          toast.error('Your session has expired. Please log in again.');
        } else {
          toast.error('Failed to fetch KYC status');
        }
        kycStatus.value = { status: 'not_submitted' };
      }
    };

    const submitKyc = async () => {
      if (!canSubmit.value) return;

      loading.value = true;
      const formData = new FormData();
      formData.append('selfie', dataURLtoFile(capturedSelfie.value, 'selfie.jpg'));
      formData.append('passport', passportFile.value);

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          toast.error('Please log in to submit KYC verification');
          return;
        }

        const res = await apiClient.post('/kyc/verify', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
        });
        if (res.ok) {
          kycStatus.value = {
            status: res.data.status,
            submitted_at: new Date().toISOString(),
            verified_at: res.data.verified_at
          };
        } else {
          res.errors.forEach(e => toast.error(e));
        }
        toast.success('KYC verification submitted successfully');
        clearForm();
        showVerificationForm.value = false;
      } catch (error) {
        console.error('Error submitting KYC:', error);
        if (error.response?.status === 401) {
          toast.error('Your session has expired. Please log in again.');
        } else {
          toast.error(error.response?.data?.message || 'Failed to submit KYC verification');
        }
      } finally {
        loading.value = false;
      }
    };

    const dataURLtoFile = (dataurl, filename) => {
      const arr = dataurl.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    };

    onMounted(() => {
      fetchKycStatus();
      // Show form only if status is not submitted
      if (!kycStatus.value || kycStatus.value.status === 'not_submitted') {
        showVerificationForm.value = true;
      }
    });

    return {
      video,
      capturedSelfie,
      passportPreview,
      kycStatus,
      loading,
      canSubmit,
      currentStep,
      steps,
      getStatusBadgeClass,
      getStatusIcon,
      getStatusText,
      startCamera,
      captureSelfie,
      retakeSelfie,
      handlePassportUpload,
      handleDrop,
      removePassport,
      submitKyc,
      fetchKycStatus,
      nextStep,
      previousStep,
      goToStep,
      resetVerification,
      showVerificationForm,
    };
  },
};
</script>

<style scoped>
.kyc-verification {
  min-height: 100vh;
  background-color: var(--bs-container-bg);
  padding: 2rem 0;
}

/* Status States */
.success-state,
.pending-state,
.failed-state {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  padding: 3rem 2rem;
  border-radius: 16px;
  background: var(--bs-card2-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.success-icon,
.pending-icon,
.failed-icon {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  font-size: 3rem;
  transition: all 0.3s ease;
}

.success-icon {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  box-shadow: 0 4px 20px rgba(40, 167, 69, 0.2);
}

.pending-icon {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  box-shadow: 0 4px 20px rgba(255, 193, 7, 0.2);
}

.failed-icon {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  box-shadow: 0 4px 20px rgba(220, 53, 69, 0.2);
}

/* Upload Area */
.upload-area {
  border: 2px dashed var(--bs-border-color);
  border-radius: 16px;
  padding: 3rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: rgba(var(--bs-primary-rgb), 0.02);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.upload-area:hover {
  border-color: var(--bs-primary);
  background: rgba(var(--bs-primary-rgb), 0.05);
  transform: translateY(-2px);
}

.upload-area.has-file {
  border-color: var(--bs-primary);
  background: rgba(var(--bs-primary-rgb), 0.05);
}

.upload-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(var(--bs-primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: var(--bs-primary);
  transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
  transform: scale(1.1);
}

.upload-text {
  max-width: 400px;
  margin: 0 auto;
}

.upload-text p {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--bs-body-color);
}

/* Camera Container */
.camera-container {
  background: rgba(var(--bs-primary-rgb), 0.02);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.camera-preview {
  width: 100%;
  border-radius: 12px;
  aspect-ratio: 16/9;
  object-fit: cover;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.camera-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1.5rem;
}

/* Review Cards */
.review-card {
  background: rgba(var(--bs-primary-rgb), 0.02);
  border-radius: 16px;
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid rgba(var(--bs-primary-rgb), 0.1);
  overflow: hidden;
}

.review-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(var(--bs-primary-rgb), 0.1);
}

.review-card .card-body {
  padding: 1.5rem;
}

.review-card .card-title {
  font-size: 1.2rem;
  color: var(--bs-primary);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.review-image {
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.review-image img {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.review-card:hover .review-image img {
  transform: scale(1.05);
}

/* Progress Steps */
.progress-steps {
  position: relative;
  padding: 0 3rem;
  margin-bottom: 3rem;
}

.progress-line {
  position: absolute;
  top: 2rem;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--bs-border-color);
  z-index: 1;
}

.step-item {
  position: relative;
  z-index: 2;
  text-align: center;
  flex: 1;
}

.step-number {
  width: 4rem;
  height: 4rem;
  background: var(--bs-card2-bg);
  border: 2px solid var(--bs-border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-weight: 600;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.step-label {
  font-size: 1rem;
  color: var(--bs-muted);
  transition: all 0.3s ease;
  font-weight: 500;
}

.step-item.active .step-number {
  border-color: var(--bs-primary);
  background: var(--bs-primary);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.2);
}

.step-item.active .step-label {
  color: var(--bs-primary);
  font-weight: 600;
}

.step-item.completed .step-number {
  border-color: var(--bs-success);
  background: var(--bs-success);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.step-item.completed .step-label {
  color: var(--bs-success);
}

/* Process Card */
.process-card {
  animation: fadeIn 0.3s ease-out;
  padding: 2rem;
  background: var(--bs-card2-bg);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(var(--bs-primary-rgb), 0.1);
}

.process-icon {
  position: relative;
  display: inline-block;
  margin-bottom: 2rem;
}

.process-icon i {
  font-size: 2.5rem;
  color: var(--bs-primary);
  background: rgba(var(--bs-primary-rgb), 0.1);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.process-card:hover .process-icon i {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.2);
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--bs-primary);
  border-color: var(--bs-primary);
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.2);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(var(--bs-primary-rgb), 0.3);
}

.btn-outline-primary {
  border: 2px solid var(--bs-primary);
  color: var(--bs-primary);
}

.btn-outline-primary:hover {
  background: var(--bs-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.2);
}

.btn-success {
  background: var(--bs-success);
  border-color: var(--bs-success);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
}

.btn-danger {
  background: var(--bs-danger);
  border-color: var(--bs-danger);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(220, 53, 69, 0.3);
}

/* Help Card */
.help-card {
  background: rgba(var(--bs-primary-rgb), 0.02);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  border: 1px solid rgba(var(--bs-primary-rgb), 0.1);
}

.help-card i {
  font-size: 2rem;
  color: var(--bs-primary);
  margin-bottom: 1rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Button Styles */
.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .progress-steps {
    padding: 0 1rem;
  }

  .step-number {
    width: 3rem;
    height: 3rem;
    font-size: 1rem;
  }

  .step-label {
    font-size: 0.875rem;
  }

  .process-card {
    padding: 1.5rem;
  }

  .upload-area {
    padding: 2rem 1rem;
  }

  .camera-container {
    padding: 1rem;
  }

  .camera-controls {
    flex-direction: column;
  }

  .camera-controls .btn {
    width: 100%;
  }
}

/* Card Body Max Width */
.card-body {
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Dashboard Card */
.dashboard-card {
  .help-card {
    margin-top: 4rem;
    padding: 2rem;
    border-radius: 16px;
    background: rgba(var(--bs-primary-rgb), 0.02);
    border: 1px solid rgba(var(--bs-primary-rgb), 0.1);
  }
}
</style>
