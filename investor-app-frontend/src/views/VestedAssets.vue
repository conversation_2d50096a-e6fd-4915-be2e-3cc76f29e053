<template>
  <div class="container mt-5">
    <h2 class="text-center mb-4">Vested Assets</h2>

    <!-- 🔹 Filters Section -->
    <div class="filters-container mb-4">
      <!-- 🔹 Filter by Asset Type -->
      <div class="filter-item">
        <label class="form-label">Asset Type</label>
        <div class="input-group">
          <span class="input-group-text"><i class="bi bi-box"></i></span>
          <select v-model="selectedTypeFilter" class="form-control">
            <option value="">All Types</option>
            <option value="equities">Equities</option>
            <option value="real_estate">Real Estate</option>
            <option value="bonds">Bonds</option>
          </select>
        </div>
      </div>

      <!-- 🔹 Filter by Vesting Status -->
      <div class="filter-item">
        <label class="form-label">Vesting Status</label>
        <div class="input-group">
          <span class="input-group-text"><i class="bi bi-clock"></i></span>
          <select v-model="selectedStatusFilter" class="form-control">
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="paused">Paused</option>
            <option value="completed">Completed</option>
            <option value="terminated">Terminated</option>
          </select>
        </div>
      </div>

      <!-- 🔹 Search Input -->
      <div class="filter-item">
        <label class="form-label">Search</label>
        <div class="input-group">
          <span class="input-group-text"><i class="bi bi-search"></i></span>
          <input v-model="searchQuery" type="text" class="form-control" placeholder="Search assets..." />
        </div>
      </div>
    </div>

    <!-- 🔹 Vested Assets List -->
    <div class="card">
      <div class="card-body">
        <div class="table-responsive">
          <table class="table modern-table">
            <thead>
              <tr>
                <th>Asset Name</th>
                <th>Type</th>
                <th>Total Value</th>
                <th>Vested Amount</th>
                <th>Vesting Progress</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="asset in filteredAssets" :key="asset.asset_id">
                <td>{{ asset.asset_name }}</td>
                <td>
                  <span :class="`badge badge-${getTypeClass(asset.asset_type)}`">
                    {{ asset.asset_type }}
                  </span>
                </td>
                <td class="text-end">${{ formatNumber(asset.asset_value) }}</td>
                <td class="text-end">${{ formatNumber(asset.vested_amount) }}</td>
                <td>
                  <div class="progress">
                    <div class="progress-bar"
                         :style="{ width: `${calculateVestingProgress(asset)}%` }"
                         :class="getProgressBarClass(asset)">
                      {{ calculateVestingProgress(asset) }}%
                    </div>
                  </div>
                </td>
                <td>
                  <span :class="`status-badge ${getStatusClass(asset.vesting_status)}`">
                    {{ asset.vesting_status }}
                  </span>
                </td>
                <td>
                  <div class="btn-group">
                    <button @click="openDetailsModal(asset)" class="btn btn-sm btn-outline-primary">
                      <i class="bi bi-eye"></i>
                    </button>
                    <button v-if="isManager && asset.vesting_status === 'active'"
                            @click="pauseVesting(asset)"
                            class="btn btn-sm btn-outline-warning">
                      <i class="bi bi-pause"></i>
                    </button>
                    <button v-if="isManager && asset.vesting_status === 'paused'"
                            @click="resumeVesting(asset)"
                            class="btn btn-sm btn-outline-success">
                      <i class="bi bi-play"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 🔹 Asset Details Modal -->
    <div v-if="selectedAsset" class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Vesting Contract Details</h5>
            <button type="button" class="btn-close" @click="closeDetailsModal"><i class="bi bi-x"></i></button>
          </div>
          <div class="modal-body">
            <div class="row g-3">
              <!-- Asset Information -->
              <div class="col-md-6">
                <div class="info-card">
                  <label class="info-label">Asset Name</label>
                  <p class="info-value">{{ selectedAsset.asset_name }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-card">
                  <label class="info-label">Asset Type</label>
                  <p class="info-value">
                    <span :class="`badge badge-${getTypeClass(selectedAsset.asset_type)}`">
                      {{ selectedAsset.asset_type }}
                    </span>
                  </p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-card">
                  <label class="info-label">Total Value</label>
                  <p class="info-value">${{ formatNumber(selectedAsset.asset_value) }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-card">
                  <label class="info-label">Vested Amount</label>
                  <p class="info-value">${{ formatNumber(selectedAsset.vested_amount) }}</p>
                </div>
              </div>

              <!-- Vesting Schedule -->
              <div class="col-12">
                <h6 class="section-title">Vesting Schedule</h6>
                <div class="info-card">
                  <div class="row g-3">
                    <div class="col-md-4">
                      <label class="info-label">Start Date</label>
                      <p class="info-value">{{ formatDate(selectedAsset.vesting_schedule.start_date) }}</p>
                    </div>
                    <div class="col-md-4">
                      <label class="info-label">Duration</label>
                      <p class="info-value">{{ formatDuration(selectedAsset.vesting_schedule.duration) }}</p>
                    </div>
                    <div class="col-md-4">
                      <label class="info-label">Cliff Period</label>
                      <p class="info-value">{{ formatDuration(selectedAsset.vesting_schedule.cliff_period) }}</p>
                    </div>
                    <div class="col-md-4">
                      <label class="info-label">Release Frequency</label>
                      <p class="info-value">{{ selectedAsset.vesting_schedule.release_frequency }}</p>
                    </div>
                    <div class="col-md-4">
                      <label class="info-label">Contract Address</label>
                      <p class="info-value">
                        <a :href="getEtherscanLink(selectedAsset.vesting_contract_address)"
                           target="_blank"
                           class="hash-link">
                          {{ shortenHash(selectedAsset.vesting_contract_address) }}
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Beneficiaries -->
              <div class="col-12">
                <h6 class="section-title">Beneficiaries</h6>
                <div class="table-responsive">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Percentage</th>
                        <th>Vested Amount</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="beneficiary in selectedAsset.beneficiaries" :key="beneficiary.beneficiary_id">
                        <td>{{ beneficiary.name }}</td>
                        <td>{{ beneficiary.percentage }}%</td>
                        <td>${{ formatNumber(calculateBeneficiaryVestedAmount(selectedAsset, beneficiary)) }}</td>
                        <td>
                          <span :class="`status-badge ${getBeneficiaryStatusClass(selectedAsset, beneficiary)}`">
                            {{ getBeneficiaryStatus(selectedAsset, beneficiary) }}
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="selectedAsset" class="modal-backdrop fade show"></div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import apiClient from '@/services/apiClient';
import { useToast } from 'vue-toastification';

export default {
  setup() {
    const toast = useToast();
    const authStore = useAuthStore();
    const vestedAssets = ref([]);
    const selectedAsset = ref(null);
    const selectedTypeFilter = ref('');
    const selectedStatusFilter = ref('');
    const searchQuery = ref('');

    const isManager = computed(() => authStore.user?.role === 'manager');

    const fetchVestedAssets = async () => {
      try {
        const response = await apiClient.get('/vesting/user');
        vestedAssets.value = response.data.vestingRecords;
      } catch (error) {
        toast.error('Failed to fetch vested assets');
        console.error('Error fetching vested assets:', error);
      }
    };

    const filteredAssets = computed(() => {
      let filtered = vestedAssets.value;

      if (selectedTypeFilter.value) {
        filtered = filtered.filter(asset => asset.asset_type === selectedTypeFilter.value);
      }

      if (selectedStatusFilter.value) {
        filtered = filtered.filter(asset => asset.vesting_status === selectedStatusFilter.value);
      }

      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(asset =>
          asset.asset_name.toLowerCase().includes(query) ||
          asset.asset_type.toLowerCase().includes(query)
        );
      }

      return filtered;
    });

    const openDetailsModal = (asset) => {
      selectedAsset.value = asset;
    };

    const closeDetailsModal = () => {
      selectedAsset.value = null;
    };

    const formatNumber = (value) => {
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    };

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const formatDuration = (days) => {
      const years = Math.floor(days / 365);
      const months = Math.floor((days % 365) / 30);
      const remainingDays = days % 30;

      const parts = [];
      if (years > 0) parts.push(`${years} year${years > 1 ? 's' : ''}`);
      if (months > 0) parts.push(`${months} month${months > 1 ? 's' : ''}`);
      if (remainingDays > 0) parts.push(`${remainingDays} day${remainingDays > 1 ? 's' : ''}`);

      return parts.join(', ');
    };

    const calculateVestingProgress = (asset) => {
      const startDate = new Date(asset.vesting_schedule.start_date);
      const duration = asset.vesting_schedule.duration;
      const cliffPeriod = asset.vesting_schedule.cliff_period;
      const now = new Date();
      const elapsedDays = Math.floor((now - startDate) / (1000 * 60 * 60 * 24));

      if (elapsedDays < cliffPeriod) return 0;
      if (elapsedDays >= duration) return 100;

      const vestingPeriod = duration - cliffPeriod;
      const vestedDays = elapsedDays - cliffPeriod;
      return Math.min(100, Math.round((vestedDays / vestingPeriod) * 100));
    };

    const getProgressBarClass = (asset) => {
      const progress = calculateVestingProgress(asset);
      if (progress === 0) return 'bg-secondary';
      if (progress < 50) return 'bg-warning';
      if (progress < 100) return 'bg-info';
      return 'bg-success';
    };

    const getTypeClass = (type) => ({
      equities: 'primary',
      real_estate: 'success',
      bonds: 'warning'
    }[type] || 'secondary');

    const getStatusClass = (status) => ({
      active: 'text-success',
      paused: 'text-warning',
      completed: 'text-info',
      terminated: 'text-danger'
    }[status] || 'text-muted');

    const getBeneficiaryStatus = (asset, beneficiary) => {
      const progress = calculateVestingProgress(asset);
      if (progress === 0) return 'Not Started';
      if (progress < 100) return 'In Progress';
      return 'Completed';
    };

    const getBeneficiaryStatusClass = (asset, beneficiary) => {
      const status = getBeneficiaryStatus(asset, beneficiary);
      return {
        'Not Started': 'text-secondary',
        'In Progress': 'text-warning',
        'Completed': 'text-success'
      }[status] || 'text-muted';
    };

    const calculateBeneficiaryVestedAmount = (asset, beneficiary) => {
      const progress = calculateVestingProgress(asset);
      return (asset.asset_value * beneficiary.percentage / 100) * (progress / 100);
    };

    const getEtherscanLink = (address) => {
      return `https://etherscan.io/address/${address}`;
    };

    const shortenHash = (hash) => {
      if (!hash) return '';
      return `${hash.substring(0, 6)}...${hash.substring(hash.length - 4)}`;
    };

    const pauseVesting = async (asset) => {
      try {
        await apiClient.patch(`/vesting/${asset.vesting_contract_id}/status`, {
          status: 'paused'
        });
        toast.success('Vesting contract paused successfully');
        fetchVestedAssets();
      } catch (error) {
        toast.error('Failed to pause vesting contract');
        console.error('Error pausing vesting:', error);
      }
    };

    const resumeVesting = async (asset) => {
      try {
        await apiClient.patch(`/vesting/${asset.vesting_contract_id}/status`, {
          status: 'active'
        });
        toast.success('Vesting contract resumed successfully');
        fetchVestedAssets();
      } catch (error) {
        toast.error('Failed to resume vesting contract');
        console.error('Error resuming vesting:', error);
      }
    };

    onMounted(() => {
      fetchVestedAssets();
    });

    return {
      isManager,
      vestedAssets,
      selectedAsset,
      selectedTypeFilter,
      selectedStatusFilter,
      searchQuery,
      filteredAssets,
      openDetailsModal,
      closeDetailsModal,
      formatNumber,
      formatDate,
      formatDuration,
      calculateVestingProgress,
      getProgressBarClass,
      getTypeClass,
      getStatusClass,
      getBeneficiaryStatus,
      getBeneficiaryStatusClass,
      calculateBeneficiaryVestedAmount,
      getEtherscanLink,
      shortenHash,
      pauseVesting,
      resumeVesting
    };
  }
};
</script>

<style scoped>
.filters-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.info-card {
  background: rgba(var(--bs-primary-rgb), 0.05);
  border-radius: 8px;
  padding: 1rem;
  height: 100%;
}

.info-label {
  font-size: 0.875rem;
  color: var(--bs-muted);
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--bs-primary);
  margin-bottom: 1rem;
}

.progress {
  height: 8px;
  background-color: var(--bs-gray-200);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  transition: width 0.3s ease;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.hash-link {
  color: var(--bs-primary);
  text-decoration: none;
  font-family: monospace;
  font-size: 0.875rem;
}

.hash-link:hover {
  text-decoration: underline;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1040;
}
</style>
