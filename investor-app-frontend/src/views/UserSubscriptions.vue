<template>
  <div class="container mt-5">
    <h2 class="text-center mb-4">Manage Your Subscriptions</h2>

    <!-- Feedback Alerts -->
    <div v-if="successMessage" class="alert alert-success" role="alert">
      {{ successMessage }}
    </div>
    <div v-if="errorMessage" class="alert alert-danger" role="alert">
      {{ errorMessage }}
    </div>

    <!-- Subscription Table -->
    <div v-if="loading" class="text-center">
      <p>Loading subscriptions...</p>
    </div>
    <div v-else class="card ">
      <div class="card-body">
        <h5 class="card-title">Current Subscriptions</h5>
        <table class="table table-striped mt-3" v-if="subscriptions.length">
          <thead>
            <tr>
              <th>Payment Method</th>
              <th>Status</th>
              <th>Created At</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="subscription in subscriptions" :key="subscription.id">
              <td>{{ subscription.payment_method }}</td>
              <td>{{ subscription.status }}</td>
              <td>{{ new Date(subscription.created_at).toLocaleDateString() }}</td>
              <td>
                <button class="btn btn-danger btn-sm" @click="cancelSubscription(subscription.id)">Cancel</button>
              </td>
            </tr>
          </tbody>
        </table>
        <p v-else>No subscriptions found.</p>
      </div>
    </div>

    <!-- Add Subscription Form -->
    <div v-if="!hasActiveSubscription" class="card  mt-4">
      <div class="card-body">
        <h5 class="card-title">Add Subscription</h5>
        <form @submit.prevent="addSubscription">
          <div class="mb-3">
            <label for="paymentMethod" class="form-label">Payment Method</label>
            <select id="paymentMethod" v-model="newSubscription.payment_method" class="form-control" required>
              <option value="" disabled>Select a payment method</option>
              <option value="stripe">Stripe</option>
              <option value="btcpay">BTC Pay</option>
            </select>
          </div>
          <div class="mb-3" v-if="pricing.length">
            <label for="subscriptionPlan" class="form-label">Subscription Plan</label>
            <select id="subscriptionPlan" v-model="newSubscription.planId" class="form-control" required>
              <option value="" disabled>Select a subscription plan</option>
              <option v-for="plan in pricing" :key="plan.plan_id" :value="plan.plan_id">
                {{ plan.name }} - ${{ (plan.cost / 100).toFixed(2) }}
              </option>
            </select>
          </div>
          <button type="submit" class="btn btn-primary w-100" :disabled="loadingAdd || isStripePayment">
            <span v-if="loadingAdd" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            <span v-if="!loadingAdd && !isStripePayment">Add Subscription</span>
          </button>
        </form>
      </div>
    </div>

    <!-- Stripe Payments Form -->
    <StripePaymentsForm v-if="isStripePayment" :clientSecret="stripeClientSecret" @payment-success="handleStripeSuccess"
      @payment-error="handleStripeFailure" @close="handleStripeClose" />
  </div>
</template>

<script>
import apiClient from "@/services/apiClient";
import { useAuthStore } from "@/stores/auth";
import StripePaymentsForm from "@/views/StripePaymentsForm.vue";

export default {
  components: { StripePaymentsForm },
  setup() {
    const authStore = useAuthStore();
    return {
      userId: authStore.userId,
      token: authStore.token,
    };
  },
  data() {
    return {
      pricing: [],
      subscriptions: [],
      newSubscription: {
        payment_method: "",
        planId: "",
      },
      successMessage: "",
      errorMessage: "",
      loading: true,
      loadingAdd: false,
      isStripePayment: false,
      stripeClientSecret: null,
    };
  },
  computed: {
    hasActiveSubscription() {
      return this.subscriptions.some(
        (subscription) => subscription.status === "active"
      );
    },
  },
  async created() {
    if (!this.userId || !this.token) {
      this.errorMessage = "Authentication error. Please log in again.";
      this.$router.push("/login");
      return;
    }

    await this.fetchSubscriptions();

    try {
      const pricingResponse = await apiClient.get('/subscriptions/pricing');
      if (pricingResponse.ok) {
        this.pricing = pricingResponse.data;
      }
    } catch (error) {
      console.error("Error fetching pricing:", error);
    }
  },
  methods: {
    async fetchSubscriptions() {
      this.loading = true;
      this.errorMessage = "";

      try {
        const response = await apiClient.get(`/subscriptions/${this.userId}`);
        if (response.ok) {
          this.subscriptions = response.data.subscriptions;
        } else {
          throw new Error(response.errors[0]);
        }
      } catch (error) {
        this.errorMessage =
          error.response?.data?.error || "Failed to load subscriptions.";
        console.error(
          "Error fetching subscriptions:",
          error.response?.data || error.message
        );
      } finally {
        this.loading = false;
      }
    },
    async addSubscription() {
      console.log(this.newSubscription.payment_method, this.newSubscription.planId)
      if (!this.newSubscription.payment_method || !this.newSubscription.planId) {
        this.errorMessage = 'Please select a payment method and a subscription plan.';
        return;
      }

      this.loadingAdd = true;
      this.successMessage = '';
      this.errorMessage = '';

      // Debugging: Log the payload
      console.log('Adding subscription with:', this.newSubscription);

      try {
        const response = await apiClient.post(`/subscriptions/${this.newSubscription.payment_method}/create`,
          { ...this.newSubscription, userId: this.userId });
        if (response.ok) {
          this.successMessage = 'Subscription added successfully!';
          this.subscriptions.push(response.data.subscription);
        } else {
          throw new Error(response.errors[0]);
        }
        this.newSubscription.payment_method = '';
        this.newSubscription.planId = '';
      } catch (error) {
        const backendMessage = error.response?.data?.error;
        this.errorMessage = backendMessage || 'Failed to add subscription due to a network issue.';
        console.error('Error adding subscription:', error.response?.data || error.message);
      } finally {
        this.loadingAdd = false;
      }
    },
    handleStripeSuccess() {
      this.successMessage = "Payment successful!";
      this.isStripePayment = false;
      this.fetchSubscriptions();
    },
    handleStripeFailure(error) {
      this.errorMessage = `Payment failed: ${error}`;
    },
    handleStripeClose() {
      this.isStripePayment = false;
    },
  },
};
</script>
