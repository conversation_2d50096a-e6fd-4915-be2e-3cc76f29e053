<template>
  <div class="reports-container container">
    <div class="reports-header">
      <h1>Reports Dashboard</h1>
      <div class="header-actions">
        <button @click="openExportModal" class="btn btn-primary">
          <i class="fas fa-download"></i> Export Report
        </button>
      </div>
      <div class="filter-controls">
        <div class="date-range">
          <label>Date Range:</label>
          <input type="date" v-model="filters.startDate" />
          <span>to</span>
          <input type="date" v-model="filters.endDate" />
        </div>

        <div class="asset-filters" v-if="userRole === 'manager'">
          <label>Clients:</label>
          <select v-model="filters.clientIds" multiple>
            <option value="">All Clients</option>
            <option v-for="client in clients" :key="client.id" :value="client.id">
              {{ client.company_name }}
            </option>
          </select>
        </div>

        <div class="asset-filters">
          <label>Asset Type:</label>
          <select v-model="filters.assetType">
            <option value="">All Types</option>
            <option v-for="type in assetTypes" :key="type" :value="type">
              {{ type }}
            </option>
          </select>
        </div>

        <div class="asset-filters">
          <label>Approval Status:</label>
          <select v-model="filters.approvalStatus">
            <option value="">All Statuses</option>
            <option value="approved">Approved</option>
            <option value="pending">Pending</option>
          </select>
        </div>

        <div class="value-range">
          <label>Value Range:</label>
          <input type="number" v-model="filters.minValue" placeholder="Min" />
          <span>to</span>
          <input type="number" v-model="filters.maxValue" placeholder="Max" />
        </div>

        <button @click="applyFilters" class="btn btn-primary">Apply Filters</button>
      </div>
    </div>

    <div class="reports-content">
      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        <div class="spinner"></div>
        <p>Loading reports data...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">⚠️</div>
        <h3>Error Loading Reports</h3>
        <p>{{ error }}</p>
        <button @click="fetchData" class="btn btn-primary">Retry</button>
      </div>

      <!-- No Data State -->
      <div v-else-if="!loading && assets.length === 0" class="no-data-state">
        <div class="no-data-icon">📊</div>
        <h3>No Data Available</h3>
        <p>There are no assets matching your current filters.</p>
        <button @click="resetFilters" class="btn btn-primary">Reset Filters</button>
      </div>

      <!-- Data Display -->
      <template v-else>
        <div class="charts-row">
          <div class="chart-container">
            <h3>Asset Distribution</h3>
            <apexchart
              v-if="pieChartSeries.length > 0"
              type="pie"
              :options="pieChartOptions"
              :series="pieChartSeries"
            />
            <div v-else class="no-data-message">
              No asset distribution data available
            </div>
          </div>
          <div class="chart-container">
            <h3>Asset Value Over Time</h3>
            <apexchart
              v-if="lineChartSeries[0].data.length > 0"
              type="line"
              :options="lineChartOptions"
              :series="lineChartSeries"
            />
            <div v-else class="no-data-message">
              No historical value data available
            </div>
          </div>
        </div>

        <div class="statistics-grid">
          <div class="stat-card">
            <h4>Total Assets</h4>
            <p class="stat-value">{{ formatHumanReadable(statistics.totalAssets) }}</p>
            <p class="stat-full-value" data-tooltip="Total Assets">{{ formatNumber(statistics.totalAssets) }}</p>
          </div>
          <div class="stat-card">
            <h4>Total Value</h4>
            <p class="stat-value">{{ formatHumanReadable(statistics.totalValue, true) }}</p>
            <p class="stat-full-value" data-tooltip="Total Value">${{ formatNumber(statistics.totalValue) }}</p>
          </div>
          <div class="stat-card">
            <h4>Average Asset Value</h4>
            <p class="stat-value">{{ formatHumanReadable(statistics.averageValue, true) }}</p>
            <p class="stat-full-value" data-tooltip="Average Asset Value">${{ formatNumber(statistics.averageValue) }}</p>
          </div>
          <div class="stat-card">
            <h4>Approval Rate</h4>
            <p class="stat-value">{{ statistics.approvalRate || 0 }}%</p>
            <p class="stat-full-value" data-tooltip="Approval Rate">{{ statistics.approvalRate || 0 }}%</p>
          </div>
        </div>

        <div class="assets-table">
          <h3>Asset Details</h3>
          <table class="table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Value</th>
                <th>Status</th>
                <th>Ownership</th>
                <th>Owner</th>
                <th>Created</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="asset in assets"
                  :key="asset.id"
                  @click="openAssetDetails(asset.id)"
                  class="clickable-row">
                <td>{{ asset.name }}</td>
                <td>{{ asset.type }}</td>
                <td>${{ formatCurrency(asset.value) }}</td>
                <td>
                  <span :class="['status-badge', asset.approval_status]">
                    {{ asset.approval_status }}
                  </span>
                </td>
                <td>{{ asset.ownership_percentage }}%</td>
                <td>
                  <span v-if="asset.is_creator" class="creator-badge">You</span>
                  <span v-else>{{ asset.owner_company || 'Unknown' }}</span>
                </td>
                <td>{{ formatDate(asset.created_at) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>
    </div>

    <!-- Asset Details Modal -->
    <AssetDetailsModal
      v-if="selectedAssetId"
      :isOpen="!!selectedAssetId"
      :asset="selectedAsset"
      :isManager="userRole === 'manager'"
      @close="closeAssetDetails"
      @update-ownership="handleOwnershipUpdate"
    />

    <!-- Export Modal -->
    <ReportExportModal
      v-if="showExportModal"
      :isOpen="showExportModal"
      :filters="filters"
      :assets="assets"
      :statistics="statistics"
      @close="closeExportModal"
    />
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import apiClient from '@/services/apiClient';
import dayjs from 'dayjs';
import AssetDetailsModal from '@/components/AssetDetailsModal.vue';
import ReportExportModal from '@/components/ReportExportModal.vue';

export default {
  name: 'ReportsView',
  components: {
    AssetDetailsModal,
    ReportExportModal
  },
  setup() {
    const authStore = useAuthStore();
    const userRole = computed(() => authStore.user?.role);
    const error = ref(null);
    const selectedAssetId = ref(null);
    const selectedAsset = ref(null);
    const loadingAsset = ref(false);

    const filters = ref({
      clientIds: [],
      assetType: '',
      approvalStatus: '',
      startDate: '',
      endDate: '',
      minValue: '',
      maxValue: ''
    });

    const assets = ref([]);
    const clients = ref([]);
    const statistics = ref({
      totalAssets: 0,
      totalValue: 0,
      averageValue: 0,
      approvalRate: 0
    });

    const assetTypes = ref([]);
    const loading = ref(false);

    const pieChartOptions = ref({
      chart: {
        type: 'pie',
        background: 'transparent'
      },
      labels: [],
      colors: [
        'var(--bs-primary)', // Primary blue
        'var(--bs-accent-teal)', // Teal
        'var(--bs-accent-blue)', // Secondary blue
        'var(--bs-accent-green)', // Green
        'var(--bs-accent-navy)', // Navy
        '#8B5CF6'  // Purple (kept for variety)
      ],
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: 200
          },
          legend: {
            position: 'bottom'
          }
        }
      }],
      theme: {
        mode: document.documentElement.getAttribute('data-bs-theme') || 'light'
      },
      legend: {
        labels: {
          colors: 'var(--bs-body-color)'
        }
      }
    });

    const pieChartSeries = ref([]);

    const lineChartOptions = ref({
      chart: {
        type: 'line',
        height: 350,
        background: 'transparent',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: true
          }
        }
      },
      colors: ['var(--bs-primary)'],
      stroke: {
        width: 3,
        curve: 'smooth'
      },
      markers: {
        size: 5,
        colors: ['var(--bs-primary)'],
        strokeColors: 'var(--bs-body-bg)',
        strokeWidth: 2
      },
      xaxis: {
        type: 'datetime',
        labels: {
          style: {
            colors: 'var(--bs-body-color)'
          }
        },
        title: {
          style: {
            color: 'var(--bs-body-color)'
          }
        }
      },
      yaxis: {
        title: {
          text: 'Value ($)',
          style: {
            color: 'var(--bs-body-color)'
          }
        },
        labels: {
          style: {
            colors: 'var(--bs-body-color)'
          }
        }
      },
      grid: {
        borderColor: 'var(--bs-border-color)',
        strokeDashArray: 4
      },
      theme: {
        mode: document.documentElement.getAttribute('data-bs-theme') || 'light'
      },
      tooltip: {
        theme: document.documentElement.getAttribute('data-bs-theme') || 'light',
        style: {
          fontSize: '12px',
          fontFamily: 'inherit'
        },
        y: {
          formatter: function(value) {
            return '$' + value.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          }
        }
      }
    });

    const lineChartSeries = ref([{
      name: 'Asset Value',
      data: [],
      color: 'var(--bs-primary)'
    }]);

    const showExportModal = ref(false);

    const resetFilters = () => {
      filters.value = {
        clientIds: [],
        assetType: '',
        approvalStatus: '',
        startDate: '',
        endDate: '',
        minValue: '',
        maxValue: ''
      };
      fetchData();
    };

    const fetchData = async () => {
      try {
        loading.value = true;
        error.value = null;

        // Fetch asset reports
        const reportsResponse = await apiClient.get('/reports/assets', { params: filters.value });

        if (!reportsResponse.ok || !Array.isArray(reportsResponse.data)) {
          throw new Error('Invalid data received from server');
        }

        assets.value = reportsResponse.data;

        const statsResponse = await apiClient.get('/reports/statistics', {
          params: {
            startDate: filters.value.startDate,
            endDate: filters.value.endDate
          }
        });

        if (statsResponse.ok && statsResponse.data) {
          statistics.value = {
            totalAssets: statsResponse.data.totalAssets || 0,
            totalValue: statsResponse.data.totalValue || 0,
            averageValue: statsResponse.data.averageValue || 0,
            approvalRate: statsResponse.data.approvalRate || 0
          };
        }

        // Update charts
        updateCharts();

        // Fetch clients if manager
        if (userRole.value === 'manager') {
          const clientsResponse = await apiClient.get('/clients');
          clients.value = Array.isArray(clientsResponse.data) ? clientsResponse.data : [];
        }

        // Extract unique asset types
        assetTypes.value = [...new Set(assets.value.map(asset => asset.type))];
      } catch (err) {
        console.error('Error fetching report data:', err);
        error.value = err.response?.data?.message || err.message || 'Failed to fetch report data';
        assets.value = [];
        statistics.value = {
          totalAssets: 0,
          totalValue: 0,
          averageValue: 0,
          approvalRate: 0
        };
        clients.value = [];
      } finally {
        loading.value = false;
      }
    };

    const updateCharts = () => {
      if (!assets.value || !Array.isArray(assets.value)) {
        pieChartSeries.value = [];
        lineChartSeries.value = [{ data: [] }];
        return;
      }

      // Update pie chart
      const typeCounts = {};
      assets.value.forEach(asset => {
        if (asset && asset.type) {
          typeCounts[asset.type] = (typeCounts[asset.type] || 0) + 1;
        }
      });

      pieChartOptions.value.labels = Object.keys(typeCounts);
      pieChartSeries.value = Object.values(typeCounts);

      // Update line chart
      const valueData = assets.value
        .filter(asset => asset && asset.created_at && asset.value)
        .map(asset => ({
          x: new Date(asset.created_at).getTime(),
          y: parseFloat(asset.value)
        }))
        .sort((a, b) => a.x - b.x);

      lineChartSeries.value = [{
        name: 'Asset Value',
        data: valueData,
        color: 'var(--bs-primary)'
      }];
    };

    const applyFilters = () => {
      fetchData();
    };

    const formatCurrency = (value) => {
      if (typeof value !== 'number') return '$0.00';
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    };

    const formatDate = (date) => {
      if (!date) return '';
      return dayjs(date).format('MMM D, YYYY');
    };

    const formatHumanReadable = (value, isCurrency = false) => {
      if (typeof value !== 'number') return isCurrency ? '$0' : '0';

      const absValue = Math.abs(value);
      let suffix = '';
      let divisor = 1;

      if (absValue >= 1000000000) {
        suffix = 'B';
        divisor = 1000000000;
      } else if (absValue >= 1000000) {
        suffix = 'M';
        divisor = 1000000;
      } else if (absValue >= 1000) {
        suffix = 'K';
        divisor = 1000;
      }

      const formattedValue = (value / divisor).toFixed(1);
      return isCurrency
        ? `$${formattedValue}${suffix}+`
        : `${formattedValue}${suffix}+`;
    };

    const formatNumber = (value) => {
      if (typeof value !== 'number') return '0';
      return value.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    };

    const openAssetDetails = async (assetId) => {
      try {
        loadingAsset.value = true;
        selectedAssetId.value = assetId;

        const response = await apiClient.get(`/reports/assets/${assetId}`);

        if (response.ok) {
          selectedAsset.value = response.data;
        } else {
          response.errors.forEach(e => toast.error(e));
        }
      } catch (err) {
        console.error('Error fetching asset details:', err);
        error.value = err.response?.data?.message || err.message || 'Failed to fetch asset details';
      } finally {
        loadingAsset.value = false;
      }
    };

    const closeAssetDetails = () => {
      selectedAssetId.value = null;
      selectedAsset.value = null;
    };

    const handleOwnershipUpdate = async (updates) => {
      try {
        const res = await apiClient.put(`/assets/${selectedAssetId.value}/ownership`, { updates });
        if (!res.ok) {
          res.errors.forEach(e => toast.error(e));
        }
        await openAssetDetails(selectedAssetId.value);
        await fetchData();
      } catch (err) {
        console.error('Error updating ownership:', err);
        error.value = err.message || 'Failed to update ownership';
      }
    };

    const openExportModal = () => {
      showExportModal.value = true;
    };

    const closeExportModal = () => {
      showExportModal.value = false;
    };

    onMounted(() => {
      fetchData();
    });

    return {
      userRole,
      filters,
      assets,
      clients,
      statistics,
      assetTypes,
      loading,
      error,
      selectedAssetId,
      selectedAsset,
      loadingAsset,
      pieChartOptions,
      pieChartSeries,
      lineChartOptions,
      lineChartSeries,
      applyFilters,
      resetFilters,
      formatCurrency,
      formatDate,
      formatHumanReadable,
      formatNumber,
      openAssetDetails,
      closeAssetDetails,
      handleOwnershipUpdate,
      showExportModal,
      openExportModal,
      closeExportModal
    };
  }
};
</script>

<style scoped>
/* Container styling */
.reports-container {
  padding: 1.5rem 2rem;
  color: var(--bs-body-color);
}

/* Header styling */
.reports-header {
  margin-bottom: 2rem;
  padding: 2rem;

  border-radius: 16px;
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--bs-card-border-color);
}

.reports-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1rem;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.header-actions .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions .btn i {
  font-size: 1rem;
}

/* Filter controls */
.filter-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
  padding: 2rem;
  background-color: var(--bs-card2-bg);
  /* background: var(--bs-secondary-bg); */
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--bs-border-color);
}

.filter-controls label {
  font-size: 0.95rem;
  margin-bottom: 0.3rem;
  font-weight: 600;
  display: block;
  color: var(--bs-body-color);
}

.filter-controls input[type="date"],
.filter-controls input[type="number"],
.filter-controls select {
  width: 100%;
  padding: 0.6rem 0.75rem;
  border: 1px solid var(--bs-border-color);
  border-radius: 8px;
  background: var(--bs-body-bg);
  color: var(--bs-body-color);
  font-size: 0.95rem;
  transition: all 0.2s ease-in-out;
}

.filter-controls input:focus,
.filter-controls select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.1);
  border-color: var(--bs-primary);
}

/* Charts */
.charts-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-container {
background-color: var(--bs-card2-bg);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(var(--bs-border-rgb), 0.1);
}

.chart-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

.chart-container h3 {
  font-size: 1.35rem;
  font-weight: 700;
  color: var(--bs-body-color);
  margin-bottom: 1.25rem;
}

/* Statistics cards */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--bs-card2-bg);

  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.12);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.stat-card h4 {
  font-size: 1rem;
  font-weight: 500;
  color: var(--bs-gray-700);
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0.5rem 0;
  line-height: 1.2;
}

.stat-full-value {
  font-size: 0.875rem;
  color: var(--bs-gray-600);
  margin: 0;
  cursor: help;
}

/* Asset table */
.assets-table {
  background-color: var(--bs-card2-bg);

  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.assets-table h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.assets-table table {
  width: 100%;
  margin-top: 1rem;
  border-spacing: 0 0.4rem;
}

.assets-table th,
.assets-table td {
  padding: 0.65rem 0.75rem;
  text-align: left;
  font-size: 0.925rem;
  border-bottom: 1px solid var(--bs-border-color);
}

.assets-table tr:hover {
  background: var(--bs-secondary-bg);
  border-radius: 8px;
}

.assets-table .status-badge {
  padding: 0.35rem 0.75rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.approved {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status-badge.pending {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.creator-badge {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Buttons */
.btn.btn-primary {
  font-weight: 600;
  padding: 0.65rem 1.2rem;
  border-radius: 8px;
  font-size: 0.95rem;
}

/* Loading, Error, No Data */
.loading-state,
.error-state,
.no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 300px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--bs-primary);
  border-top: 4px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.no-data-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--bs-text-muted);
  font-style: italic;
}

/* Tooltip */
[data-tooltip] {
  position: relative;
  cursor: help;
}

[data-tooltip]:before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem;
  background: var(--bs-dark);
  color: white;
  border-radius: 4px;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

[data-tooltip]:hover:before {
  opacity: 1;
  visibility: visible;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .reports-container {
    padding: 1rem;
  }

  .filter-controls,
  .charts-row {
    grid-template-columns: 1fr;
  }
}

.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clickable-row:hover {
  background-color: var(--bs-secondary-bg);
}
</style>

