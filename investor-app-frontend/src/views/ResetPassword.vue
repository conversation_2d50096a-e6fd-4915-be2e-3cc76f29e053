<template>
  <div class="reset-password-container">
    <div class="container">
      <div class="row justify-content-center">
        <div class="reset-card">
          <!-- Header -->
          <div class="reset-header text-center mb-4">
            <img src="@/assets/images/alchemy finance logo.svg" alt="Archimedes Finance Logo" class="reset-logo mb-3" />
            <h3 class="fw-bold">Reset Your Password</h3>
            <p class="text-muted">Enter your email to receive a password reset link</p>
          </div>

          <!-- Success Message -->
          <transition name="fade">
            <div v-if="successMessage" class="alert alert-success d-flex align-items-center" role="alert">
              <i class="bi bi-check-circle-fill me-2"></i>
              {{ successMessage }}
            </div>
          </transition>

          <!-- Error Message -->
          <transition name="fade">
            <div v-if="errorMessage" class="alert alert-danger d-flex align-items-center" role="alert">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              {{ errorMessage }}
            </div>
          </transition>

          <!-- Reset Password Form -->
          <form @submit.prevent="submitResetRequest" class="reset-form">
            <div v-if="!isTokenValid" class="form-floating mb-3">
              <input
                id="email"
                v-model="email"
                type="email"
                class="form-control"
                placeholder="Enter your registered email"
                required
                autocomplete="email"
              />
              <label for="email">Email Address</label>
            </div>

            <div v-else>
              <div class="form-floating mb-3">
                <div class="input-group">
                  <input
                    id="newPassword"
                    v-model="newPassword"
                    :type="showPassword ? 'text' : 'password'"
                    class="form-control"
                    placeholder="Enter a new password"
                    required
                    autocomplete="new-password"
                  />
                  <button
                    class="btn btn-outline-secondary"
                    type="button"
                    @click="showPassword = !showPassword"
                    aria-label="Toggle password visibility"
                  >
                    <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                  </button>
                </div>
                <label for="newPassword">New Password</label>
              </div>

              <div class="form-floating mb-3">
                <div class="input-group">
                  <input
                    id="confirmPassword"
                    v-model="confirmPassword"
                    :type="showPassword ? 'text' : 'password'"
                    class="form-control"
                    placeholder="Confirm your new password"
                    required
                    autocomplete="new-password"
                  />
                </div>
                <label for="confirmPassword">Confirm Password</label>
              </div>
            </div>

            <!-- Robot Protection -->
            <RobotProtection @validation="handleRobotValidation" />

            <!-- Submit Button -->
            <button
              type="submit"
              class="btn btn-primary w-100 reset-btn"
              :disabled="isLoading || !isRobotValid"
            >
              <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
              {{ isTokenValid ? "Reset Password" : "Send Reset Link" }}
            </button>
          </form>

          <!-- Back to Login -->
          <div class="text-center mt-4">
            <button
              class="btn btn-link text-decoration-none"
              @click="goToLogin"
              type="button"
            >
              Back to Login
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import apiClient from "@/services/apiClient";
import RobotProtection from '@/components/RobotProtection.vue';

export default {
  components: {
    RobotProtection
  },
  data() {
    return {
      email: "",
      newPassword: "",
      confirmPassword: "",
      isLoading: false,
      isTokenValid: false,
      successMessage: null,
      errorMessage: null,
      showPassword: false,
      isRobotValid: false
    };
  },
  methods: {
    async submitResetRequest() {
      if (!this.isRobotValid) {
        this.errorMessage = 'Please complete the security check';
        return;
      }

      this.isLoading = true;
      this.successMessage = null;
      this.errorMessage = null;

      try {
        if (!this.isTokenValid) {
          const res = await apiClient.post('/auth/password/reset/request', { email: this.email });
          if (!res.ok) {
            throw new Error(res.errors[0] || 'Request failed');
          }
          this.successMessage = "Password reset link sent to your email.";
        } else {
          if (this.newPassword !== this.confirmPassword) {
            this.errorMessage = "Passwords do not match.";
            return;
          }

          const token = this.$route.query.token;
          const res = await apiClient.post('/auth/password/reset/confirm', {
            token,
            newPassword: this.newPassword,
          });
          if (!res.ok) {
            throw new Error(res.errors[0] || 'Reset failed');
          }
          this.successMessage = "Password reset successfully!";
        }
      } catch (error) {
        this.errorMessage = error.response?.data?.error || "An error occurred.";
      } finally {
        this.isLoading = false;
      }
    },
    goToLogin() {
      this.$router.push("/auth/login");
    },
    handleRobotValidation(isValid) {
      this.isRobotValid = isValid;
    }
  },
  created() {
    const token = this.$route.query.token;
    if (token) {
      this.isTokenValid = true;
    }
  },
};
</script>

<style scoped lang="scss">
.reset-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 2rem 0;
  background: var(--bs-body-bg);
}

.reset-card {
  width: 100%;
  max-width: 400px;
  background: var(--bs-card2-bg);
  border: 1px solid var(--bs-card-border-color);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.reset-logo {
  height: 48px;
  width: auto;
}

.reset-header {
  h3 {
    color: var(--bs-primary);
    margin-bottom: 0.5rem;
  }
}

.reset-form {
  .form-floating {
    .form-control {
      border-radius: 0.5rem;
      padding: 1rem 0.75rem;
      height: auto;
      min-height: 3.5rem;
      background: var(--bs-card2-bg);
      border: 1px solid var(--bs-border-color);
      color: var(--bs-body-color);
      transition: all 0.2s ease;

      &:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
      }
    }

    .input-group {
      .form-control {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      .btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }

  .reset-btn {
    height: 3.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
}

// Animations
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
