<template>
  <div class="container py-4">
    <!-- 🔹 Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="fw-bold"><i class="bi bi-people"></i> Manage Clients</h2>
      <button class="btn btn-primary" @click="fetchClients">
        <i class="bi bi-arrow-clockwise"></i> Refresh Clients
      </button>
    </div>

    <!-- 🔹 Search Unlinked Clients -->
    <div class="card mb-4">
      <div class="card-body">
        <h5 class="card-title">Search for Clients</h5>
        <input
          type="text"
          v-model="searchQuery"
          class="form-control"
          placeholder="Search by name or email..."
          @input="searchClients"
        />

        <!-- 🔹 Search Results -->
        <div v-if="searchResults.length" class="mt-3">
          <ul class="list-group">
            <li
              v-for="client in searchResults"
              :key="client.id"
              class="list-group-item d-flex justify-content-between align-items-center"
            >
              <span>{{ client.name }} ({{ client.email }})</span>
              <button class="btn btn-sm btn-outline-primary" @click="linkClient(client.id)">
                <i class="bi bi-link-45deg"></i> Link Client
              </button>
            </li>
          </ul>
        </div>
        <p v-else-if="searchQuery.trim() && !loadingSearch" class="text-muted mt-2">
          No results found.
        </p>
      </div>
    </div>

    <!-- 🔹 Linked Clients Table -->
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">Linked Clients</h5>
        <p v-if="linkedClients.length === 0" class="text-muted">No linked clients.</p>

        <div v-if="linkedClients.length">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Member Since</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="client in linkedClients" :key="client.id">
                <td>{{ client.name }}</td>
                <td>{{ client.email }}</td>
                <td>{{ formatDate(client.created_at) }}</td>
                <td>
                  <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm" @click="viewClientDashboard(client)">
                      <i class="bi bi-speedometer2"></i> Dashboard
                    </button>
                    <button class="btn btn-danger btn-sm" @click="confirmUnlink(client)">
                      <i class="bi bi-x-circle"></i> Unlink
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 🔹 Error Alert -->
    <div v-if="errorMessage" class="alert alert-danger mt-3">
      {{ errorMessage }}
    </div>

    <!-- 🔹 Unlink Confirmation Modal -->
    <div v-if="showConfirmModal" class="modal fade show d-block" tabindex="-1" role="dialog">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Confirm Unlink</h5>
            <button type="button" class="modal-close-btn" aria-label="Close" @click="cancelUnlink">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body">
            <p>
              Are you sure you want to unlink
              <strong>{{ clientToUnlink?.name }}</strong> ({{ clientToUnlink?.email }})?
            </p>
          </div>
          <div class="modal-footer">
            <button class="btn btn-secondary" @click="cancelUnlink">Cancel</button>
            <button class="btn btn-danger" @click="performUnlink">Unlink</button>
          </div>
        </div>
      </div>
      <!-- Backdrop -->
      <div class="modal-backdrop fade show"></div>
    </div>

    <!-- 🔹 Client Dashboard Modal -->
    <div v-if="showDashboardModal" class="modal fade show d-block" tabindex="-1" role="dialog">
      <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-speedometer2 me-2"></i>
              Client Dashboard: {{ selectedClient?.name }}
            </h5>
            <button type="button" class="modal-close-btn" aria-label="Close" @click="closeDashboard">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body">
            <!-- Loading State -->
            <div v-if="loadingDashboard" class="text-center py-5">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-3">Loading client dashboard data...</p>
            </div>

            <!-- Error State -->
            <div v-else-if="dashboardError" class="alert alert-danger">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              {{ dashboardError }}
            </div>

            <!-- Dashboard Content -->
            <div v-else-if="clientDashboard" class="dashboard-content">
              <div class="row g-4">
                <!-- Profile Summary -->
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h6 class="mb-0"><i class="bi bi-person me-2"></i>Profile Summary</h6>
                    </div>
                    <div class="card-body">
                      <div class="profile-summary">
                        <div class="mb-3">
                          <strong>Name:</strong> {{ clientDashboard.profile.name }}
                        </div>
                        <div class="mb-3">
                          <strong>Email:</strong> {{ clientDashboard.profile.email }}
                        </div>
                        <div class="mb-3" v-if="clientDashboard.profile.phone">
                          <strong>Phone:</strong> {{ clientDashboard.profile.phone }}
                        </div>
                        <div class="mb-3" v-if="clientDashboard.profile.address">
                          <strong>Address:</strong> {{ clientDashboard.profile.address }}
                        </div>
                        <div class="mb-3" v-if="clientDashboard.profile.tax_id">
                          <strong>Tax ID:</strong> {{ clientDashboard.profile.tax_id }}
                        </div>
                        <div class="mb-3">
                          <strong>Member Since:</strong> {{ formatDate(clientDashboard.profile.created_at) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Balance Summary -->
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h6 class="mb-0"><i class="bi bi-wallet2 me-2"></i>Balance Summary</h6>
                    </div>
                    <div class="card-body">
                      <div class="balance-summary">
                        <div class="mb-3">
                          <strong>Total Asset Value:</strong> ${{ formatNumber(clientDashboard.balances.total_value) }}
                        </div>

                        <!-- Asset Categories -->
                        <div class="asset-categories mt-4">
                          <div class="category-item mb-3" v-if="clientDashboard.balances.pending_value > 0">
                            <div class="d-flex justify-content-between mb-1">
                              <span>Pending</span>
                              <span>${{ formatNumber(clientDashboard.balances.pending_value) }}</span>
                            </div>
                            <div class="progress">
                              <div class="progress-bar bg-warning"
                                   :style="{ width: calculatePercentage(clientDashboard.balances.pending_value, clientDashboard.balances.total_value) + '%' }"></div>
                            </div>
                          </div>

                          <div class="category-item mb-3" v-if="clientDashboard.balances.approved_value > 0">
                            <div class="d-flex justify-content-between mb-1">
                              <span>Approved</span>
                              <span>${{ formatNumber(clientDashboard.balances.approved_value) }}</span>
                            </div>
                            <div class="progress">
                              <div class="progress-bar bg-success"
                                   :style="{ width: calculatePercentage(clientDashboard.balances.approved_value, clientDashboard.balances.total_value) + '%' }"></div>
                            </div>
                          </div>

                          <div class="category-item mb-3" v-if="clientDashboard.balances.tokenised_value > 0">
                            <div class="d-flex justify-content-between mb-1">
                              <span>Tokenised</span>
                              <span>${{ formatNumber(clientDashboard.balances.tokenised_value) }}</span>
                            </div>
                            <div class="progress">
                              <div class="progress-bar bg-primary"
                                   :style="{ width: calculatePercentage(clientDashboard.balances.tokenised_value, clientDashboard.balances.total_value) + '%' }"></div>
                            </div>
                          </div>

                          <div class="category-item mb-3" v-if="clientDashboard.balances.vested_value > 0">
                            <div class="d-flex justify-content-between mb-1">
                              <span>Vested</span>
                              <span>${{ formatNumber(clientDashboard.balances.vested_value) }}</span>
                            </div>
                            <div class="progress">
                              <div class="progress-bar bg-info"
                                   :style="{ width: calculatePercentage(clientDashboard.balances.vested_value, clientDashboard.balances.total_value) + '%' }"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Tokenised Assets -->
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h6 class="mb-0"><i class="bi bi-coin me-2"></i>Tokenised Assets</h6>
                    </div>
                    <div class="card-body">
                      <div v-if="clientDashboard.tokenised_assets.length === 0" class="text-center text-muted py-4">
                        <i class="bi bi-coin display-4"></i>
                        <p class="mt-3">No tokenised assets found</p>
                      </div>
                      <div v-else class="tokenised-assets-list">
                        <!-- Show only the first 3 assets by default -->
                        <div v-for="asset in displayedAssets" :key="asset.id" class="asset-item mb-3 p-3 border rounded">
                          <div class="d-flex justify-content-between align-items-center">
                            <div>
                              <h6 class="mb-1">{{ asset.name }}</h6>
                              <div class="text-muted small">{{ asset.type }} | {{ asset.approval_status }}</div>
                            </div>
                            <div class="text-end">
                              <div class="fw-bold">${{ formatNumber(asset.value) }}</div>
                              <div v-if="asset.blockchain_tx_hash" class="text-primary small">
                                <a :href="getBlockExplorerUrl(asset.blockchain_tx_hash)" target="_blank" rel="noopener noreferrer" class="blockchain-link">
                                  <i class="bi bi-link-45deg"></i> View on blockchain
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Show More / Show Less toggle button -->
                        <div v-if="clientDashboard.tokenised_assets.length > 3" class="text-center mt-2 mb-2">
                          <button @click="toggleShowAllAssets" class="btn btn-sm btn-outline-primary">
                            <i :class="showAllAssets ? 'bi bi-chevron-up' : 'bi bi-chevron-down'"></i>
                            {{ showAllAssets ? 'Show Less' : `Show ${clientDashboard.tokenised_assets.length - 3} More` }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Vesting Contracts -->
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h6 class="mb-0"><i class="bi bi-clock-history me-2"></i>Vesting Contracts</h6>
                    </div>
                    <div class="card-body">
                      <div v-if="clientDashboard.vesting_contracts.length === 0" class="text-center text-muted py-4">
                        <i class="bi bi-clock-history display-4"></i>
                        <p class="mt-3">No vesting contracts found</p>
                      </div>
                      <div v-else class="vesting-contracts-list">
                        <!-- Show only the first 3 vesting contracts by default -->
                        <div v-for="contract in displayedContracts" :key="contract.id" class="vesting-item mb-3 p-3 border rounded">
                          <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                              <h6 class="mb-1">{{ contract.asset_name }}</h6>
                              <div class="text-muted small">{{ formatDate(contract.start_date) }} - {{ formatDate(contract.end_date) }}</div>
                              <div v-if="contract.relationship_type" class="relationship-badge">
                                <span class="badge bg-info">{{ formatRelationshipType(contract.relationship_type) }}</span>
                              </div>
                            </div>
                            <div class="text-end">
                              <div class="fw-bold">${{ formatNumber(contract.value) }}</div>
                              <div class="badge" :class="getStatusBadgeClass(contract.status)">{{ contract.status }}</div>
                              <div v-if="contract.contract_address" class="text-primary small mt-1">
                                <a :href="getBlockExplorerUrl(contract.contract_address)" target="_blank" rel="noopener noreferrer" class="blockchain-link">
                                  <i class="bi bi-link-45deg"></i> View contract
                                </a>
                              </div>
                            </div>
                          </div>
                          <div class="progress mt-2" style="height: 8px;">
                            <div class="progress-bar" :class="getStatusProgressClass(contract.status)"
                                 :style="{ width: contract.progress + '%' }"></div>
                          </div>
                        </div>

                        <!-- Show More / Show Less toggle button for vesting contracts -->
                        <div v-if="clientDashboard.vesting_contracts.length > 3" class="text-center mt-2 mb-2">
                          <button @click="toggleShowAllContracts" class="btn btn-sm btn-outline-primary">
                            <i :class="showAllContracts ? 'bi bi-chevron-up' : 'bi bi-chevron-down'"></i>
                            {{ showAllContracts ? 'Show Less' : `Show ${clientDashboard.vesting_contracts.length - 3} More` }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Recent Activity -->
                <div class="col-12">
                  <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                      <h6 class="mb-0"><i class="bi bi-activity me-2"></i>Recent Activity</h6>
                    </div>
                    <div class="card-body">
                      <div v-if="clientDashboard.activities.length === 0" class="text-center text-muted py-4">
                        <i class="bi bi-activity display-4"></i>
                        <p class="mt-3">No recent activity found</p>
                      </div>
                      <div v-else class="activity-list">
                        <!-- Show only the first 5 activities by default -->
                        <div v-for="activity in displayedActivities" :key="activity.id" class="activity-item d-flex align-items-center mb-3 p-2 border-bottom">
                          <div class="activity-icon me-3">
                            <i :class="getActivityIcon(activity.type)" class="bi"></i>
                          </div>
                          <div class="activity-details flex-grow-1">
                            <div class="d-flex justify-content-between">
                              <div class="activity-message">{{ activity.message }}</div>
                              <div class="activity-time text-muted small">{{ formatActivityDate(activity.created_at) }}</div>
                            </div>
                          </div>
                        </div>

                        <!-- Show More / Show Less toggle button for activities -->
                        <div v-if="clientDashboard.activities.length > 5" class="text-center mt-2 mb-2">
                          <button @click="toggleShowAllActivities" class="btn btn-sm btn-outline-primary">
                            <i :class="showAllActivities ? 'bi bi-chevron-up' : 'bi bi-chevron-down'"></i>
                            {{ showAllActivities ? 'Show Less' : `Show ${clientDashboard.activities.length - 5} More` }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-secondary" @click="closeDashboard">Close</button>
          </div>
        </div>
      </div>
      <!-- Backdrop -->
      <div class="modal-backdrop fade show"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useAuthStore } from "@/stores/auth";
import apiClient from "@/services/apiClient";
import { useToast } from "vue-toastification"; // ✅ Import toast function

const toast = useToast(); // ✅ Get the toast instance
const authStore = useAuthStore();
const linkedClients = ref([]);
const searchQuery = ref("");
const searchResults = ref([]);
const errorMessage = ref("");
const loadingSearch = ref(false);

// New state for unlink confirmation
const showConfirmModal = ref(false);
const clientToUnlink = ref(null);

// Client Dashboard state
const showDashboardModal = ref(false);
const selectedClient = ref(null);
const clientDashboard = ref(null);
const loadingDashboard = ref(false);
const dashboardError = ref("");

// Display state for expandable sections
const showAllAssets = ref(false);
const showAllContracts = ref(false);
const showAllActivities = ref(false);

// Computed property to get the assets to display
const displayedAssets = computed(() => {
  if (!clientDashboard.value?.tokenised_assets) return [];
  return showAllAssets.value
    ? clientDashboard.value.tokenised_assets
    : clientDashboard.value.tokenised_assets.slice(0, 3);
});

// Computed property to get the vesting contracts to display
const displayedContracts = computed(() => {
  if (!clientDashboard.value?.vesting_contracts) return [];
  return showAllContracts.value
    ? clientDashboard.value.vesting_contracts
    : clientDashboard.value.vesting_contracts.slice(0, 3);
});

// Computed property to get the activities to display
const displayedActivities = computed(() => {
  if (!clientDashboard.value?.activities) return [];
  return showAllActivities.value
    ? clientDashboard.value.activities
    : clientDashboard.value.activities.slice(0, 5);
});

// Toggle functions for expandable sections
function toggleShowAllAssets() {
  showAllAssets.value = !showAllAssets.value;
}

function toggleShowAllContracts() {
  showAllContracts.value = !showAllContracts.value;
}

function toggleShowAllActivities() {
  showAllActivities.value = !showAllActivities.value;
}

// ✅ API Request Helper
async function makeRequest(endpoint, method = "get", data = {}) {
  try {
    const token = authStore.token || localStorage.getItem("token");
    if (!token) {
      console.warn("⚠️ No auth token found. The user might be logged out.");
      return { success: false, error: "Authentication required. Please log in again." };
    }
    const res = await apiClient[method.toLowerCase()](endpoint, data);
    return res.ok ? res.data : { success: false, error: res.errors[0] };
  } catch (error) {
    console.error(`❌ API Error on ${endpoint}:`, error.response?.data || error.message);

    // Handle authentication errors
    if (error.response?.status === 401 || error.response?.status === 403) {
      toast.error("Your session has expired. Please log in again.");
      authStore.logout();
      return { success: false, error: "Authentication failed. Please log in again." };
    }

    // Handle other errors
    const errorMsg = error.response?.data?.error || error.response?.data?.message || "An unexpected error occurred.";
    errorMessage.value = errorMsg;
    return { success: false, error: errorMsg };
  }
}

// ✅ Fetch Linked Clients
async function fetchClients() {
  const data = await makeRequest("/clients/");
  if (data) {
    linkedClients.value = data.clients || [];
  }
}

// ✅ Search for Unlinked Clients
async function searchClients() {
  if (!searchQuery.value.trim()) {
    searchResults.value = [];
    return;
  }
  loadingSearch.value = true;
  const data = await makeRequest(`/clients/search?query=${encodeURIComponent(searchQuery.value.trim())}`, "get");
  loadingSearch.value = false;
  if (data) {
    searchResults.value = data.clients || [];
  }
}

// ✅ Link a Client to the Manager Account with Toast Notifications
async function linkClient(clientId) {
  const data = await makeRequest("/clients/link", "post", { clientId });
  if (data) {
    toast.success("Link request sent successfully!");
    fetchClients(); // Refresh linked clients
    searchQuery.value = "";
    searchResults.value = [];
  } else {
    toast.error("Failed to send link request. Please try again.");
  }
}

// ✅ Unlink a Client (two-stage interface)
function confirmUnlink(client) {
  clientToUnlink.value = client;
  showConfirmModal.value = true;
}

function cancelUnlink() {
  clientToUnlink.value = null;
  showConfirmModal.value = false;
}

async function performUnlink() {
  if (!clientToUnlink.value) return;
  const data = await makeRequest(`/clients/unlink/${clientToUnlink.value.id}`, "DELETE", { clientId: clientToUnlink.value.id });
  if (data) {
    toast.success("Client unlinked successfully!");
    fetchClients(); // Refresh linked clients
  } else {
    toast.error("Failed to unlink client. Please try again.");
  }
  // Reset modal state
  cancelUnlink();
}

// ✅ View Client Dashboard
async function viewClientDashboard(client) {
  selectedClient.value = client;
  showDashboardModal.value = true;
  loadingDashboard.value = true;
  dashboardError.value = "";
  clientDashboard.value = null;

  try {
    // Fetch client dashboard data
    const data = await makeRequest(`/clients/${client.id}/dashboard`);
    console.log('Dashboard data response:', data);

    if (data && data.success) {
      // Ensure all required properties exist with defaults if missing
      clientDashboard.value = {
        profile: data.profile || {},
        tokenised_assets: data.tokenised_assets || [],
        vesting_contracts: data.vesting_contracts || [],
        activities: data.activities || [],
        balances: data.balances || {
          pending_value: 0,
          approved_value: 0,
          tokenised_value: 0,
          vested_value: 0,
          total_value: 0
        }
      };
    } else {
      dashboardError.value = data?.error || "Failed to load client dashboard data.";
      console.error('Dashboard error:', dashboardError.value);
    }
  } catch (error) {
    console.error("Error fetching client dashboard:", error);
    dashboardError.value = "An unexpected error occurred while loading the dashboard.";
  } finally {
    loadingDashboard.value = false;
  }
}

function closeDashboard() {
  showDashboardModal.value = false;
  selectedClient.value = null;
  clientDashboard.value = null;
  dashboardError.value = "";

  // Reset expanded state of all sections
  showAllAssets.value = false;
  showAllContracts.value = false;
  showAllActivities.value = false;
}

// ✅ Format Date
const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString();
};

// ✅ Format Number with commas
const formatNumber = (value) => {
  if (value === undefined || value === null) return "0.00";
  return Number(value).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// ✅ Calculate percentage for progress bars
const calculatePercentage = (value, total) => {
  if (!total || !value) return 0;
  return Math.min(100, Math.round((value / total) * 100));
};

// ✅ Get status badge class for vesting contracts
const getStatusBadgeClass = (status) => {
  switch (status?.toLowerCase()) {
    case "active": return "bg-success";
    case "completed": return "bg-primary";
    case "cancelled": return "bg-danger";
    case "paused": return "bg-warning";
    default: return "bg-secondary";
  }
};

// ✅ Get status progress class for vesting contracts
const getStatusProgressClass = (status) => {
  switch (status?.toLowerCase()) {
    case "active": return "bg-success";
    case "completed": return "bg-primary";
    case "cancelled": return "bg-danger";
    case "paused": return "bg-warning";
    default: return "bg-secondary";
  }
};

// ✅ Format relationship type for display
const formatRelationshipType = (type) => {
  switch (type?.toLowerCase()) {
    case "beneficiary": return "Beneficiary";
    case "creator": return "Creator";
    case "asset_owner": return "Asset Owner";
    default: return type;
  }
};

// ✅ Get activity icon based on type
const getActivityIcon = (type) => {
  switch (type?.toLowerCase()) {
    case "asset_created": return "bi-plus-circle";
    case "asset_approved": return "bi-check-circle";
    case "asset_rejected": return "bi-x-circle";
    case "asset_tokenised": return "bi-coin";
    case "vesting_created": return "bi-clock-history";
    case "login": return "bi-box-arrow-in-right";
    case "profile_updated": return "bi-person-gear";
    default: return "bi-activity";
  }
};

// ✅ Format activity date
const formatActivityDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffDay > 7) {
    return date.toLocaleDateString();
  } else if (diffDay > 0) {
    return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
  } else if (diffHour > 0) {
    return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
  } else if (diffMin > 0) {
    return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
  } else {
    return "Just now";
  }
};

// ✅ Get blockchain explorer URL for transaction hash
const getBlockExplorerUrl = (txHash) => {
  // Default to Ethereum mainnet explorer
  const network = import.meta.env.VITE_BLOCKCHAIN_NETWORK || 'ethereum';

  // Different explorers based on network
  switch(network.toLowerCase()) {
    case 'ethereum':
      return `https://etherscan.io/tx/${txHash}`;
    case 'polygon':
      return `https://polygonscan.com/tx/${txHash}`;
    case 'bsc':
    case 'binance':
      return `https://bscscan.com/tx/${txHash}`;
    case 'arbitrum':
      return `https://arbiscan.io/tx/${txHash}`;
    case 'optimism':
      return `https://optimistic.etherscan.io/tx/${txHash}`;
    case 'goerli':
      return `https://goerli.etherscan.io/tx/${txHash}`;
    case 'sepolia':
      return `https://sepolia.etherscan.io/tx/${txHash}`;
    default:
      return `https://etherscan.io/tx/${txHash}`;
  }
};

// ✅ Fetch linked clients on mount
onMounted(fetchClients);
</script>

<style scoped>
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
}
.modal-dialog {
  z-index: 1050;
}

/* Dashboard Modal Styles */
.modal-xl {
  max-width: 1140px;
}

.dashboard-content .card {
  transition: all 0.2s ease;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.dashboard-content .card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-content .card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background-color: transparent;
}

.asset-item, .vesting-item {
  transition: all 0.2s ease;
  border-radius: 6px;
}

.asset-item:hover, .vesting-item:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.activity-item {
  transition: all 0.2s ease;
}

.activity-item:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.activity-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
}

.progress {
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
}

.modal-close-btn {
  background: transparent;
  border: none;
  font-size: 1.25rem;
  line-height: 1;
  color: var(--bs-secondary);
  opacity: 0.7;
  transition: opacity 0.15s;
}

.modal-close-btn:hover {
  opacity: 1;
  color: var(--bs-primary);
}

.blockchain-link {
  text-decoration: none;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.blockchain-link:hover {
  text-decoration: underline;
  opacity: 0.9;
}

.relationship-badge {
  margin-top: 0.25rem;
}

.relationship-badge .badge {
  font-size: 0.7rem;
  font-weight: normal;
  padding: 0.25rem 0.5rem;
}

/* Show More/Less button styles */
.btn-outline-primary {
  border-color: rgba(var(--bs-primary-rgb), 0.3);
  color: var(--bs-primary);
  font-size: 0.85rem;
  padding: 0.25rem 0.75rem;
  transition: all 0.2s ease;
}

.btn-outline-primary:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  border-color: rgba(var(--bs-primary-rgb), 0.5);
  color: var(--bs-primary);
}

.btn-outline-primary i {
  margin-right: 0.25rem;
}
</style>
