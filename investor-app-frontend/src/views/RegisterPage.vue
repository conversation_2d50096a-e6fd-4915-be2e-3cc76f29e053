<template>
  <div class="d-flex flex-column align-items-center register-page">

    <!-- Top Header Section (Logo + Theme Toggle + Home) -->
    <header class="non-logged-in-header-ele">
      <a href="/" class="logo-container">
        <img src="@/assets/images/alchemy finance logo.svg" alt="Archimedes Finance Logo" class="login-logo" />
        <span class="project-name">Archimedes Finance</span>
      </a>

      <div class="header-controls">
        <!-- Theme Toggle -->
        <button @click="toggleTheme" class="btn btn-outline-secondary theme-toggle" aria-label="Toggle dark mode">
          <i :class="theme === 'light' ? 'bi bi-moon-stars' : 'bi bi-sun'"></i>
        </button>

        <!-- Home Link -->
        <RouterLink to="/" class="btn btn-outline-primary">Home</RouterLink>
      </div>
    </header>


    <div class="container">
      <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
          <div class="card register-card border-0">
            <div class="card-body">
              <!-- Dynamic Header Reflecting Account Type -->
              <h3 class="card-title text-center mb-4">{{ headerText }}</h3>

              <!-- Error Alert -->
              <div v-if="errorMessage" class="alert alert-danger" role="alert">
                <p class="mb-1">{{ errorMessage }}</p>
                <button v-if="showDetailsButton" class="btn btn-link p-0" @click="showDetails = !showDetails">
                  {{ showDetails ? 'Hide details' : 'Show technical details' }}
                </button>
                <pre v-if="showDetails" class="mt-2 p-2 rounded border">{{ errorDetails }}</pre>
              </div>

              <!-- Multi-Step Form -->
              <form @submit.prevent="handleSubmit" novalidate>
                <!-- Step 1: Account Type, Informational Text, & Role-Specific Fields -->
                <div v-if="currentStep === 1">
                  <div class="mb-4">
                    <h5>Account Type</h5>
                    <div class="btn-group" role="group" aria-label="Account Type">
                      <button type="button" class="btn"
                        :class="{ 'btn-primary': accountType === 'client', 'btn-outline-primary': accountType !== 'client' }"
                        @click="selectAccountType('client')">
                        Client
                      </button>
                      <button type="button" class="btn"
                        :class="{ 'btn-primary': accountType === 'manager', 'btn-outline-primary': accountType !== 'manager' }"
                        @click="selectAccountType('manager')">
                        Manager
                      </button>
                    </div>
                  </div>

                  <!-- Informational Text for Client or Manager Registration -->
                  <div class="mb-4" v-if="accountType === 'client'">
                    <p class="text-muted">
                      Client accounts are designed for individual investors and small businesses. If you have a
                      manager's
                      ID,
                      you can enter it below to link your account to a manager. This step is optional and can always be
                      completed later from your dashboard.
                    </p>
                  </div>
                  <div class="mb-4" v-if="accountType === 'manager'">
                    <p class="text-muted">
                      Manager accounts are intended for professionals managing client investments. As a manager, please
                      provide your company details below.
                    </p>
                  </div>

                  <!-- Role-Specific Fields -->
                  <div v-if="accountType === 'manager'">
                    <div class="mb-3">
                      <label for="companyName" class="form-label">Company Name</label>
                      <input id="companyName" v-model="companyName" type="text" class="form-control"
                        placeholder="e.g., Plains North Capital Inc" required autocomplete="organization" />
                    </div>
                    <div class="mb-3">
                      <label for="address" class="form-label">Company Address</label>
                      <textarea id="address" v-model="address" class="form-control" rows="3"
                        placeholder="Enter the company's full address" required
                        autocomplete="street-address"></textarea>
                    </div>
                  </div>
                  <div v-else>
                    <!-- Prominent Manager ID Section for Clients -->
                    <!-- Improved Optional Manager Link Section -->
                    <div class="mb-3">
                      <div class="optional-manager-link">
                        <h5>Optional Manager Link</h5>
                        <p class="small">
                          Enter your manager's ID here to link your account to a manager. This step is optional and can
                          always be completed later from your dashboard.
                        </p>
                        <label for="managerId" class="form-label">Manager ID</label>
                        <input id="managerId" v-model="managerId" type="text" class="form-control"
                          placeholder="Enter manager ID if available" autocomplete="off" />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 2: Common Personal Details -->
                <div v-if="currentStep === 2">
                  <div class="mb-3">
                    <label for="contactName" class="form-label">Contact Name</label>
                    <input id="contactName" v-model="contactName" type="text" class="form-control"
                      placeholder="e.g., John Doe" required autocomplete="name" />
                  </div>
                  <div class="mb-3">
                    <label for="email" class="form-label">Email Address</label>
                    <input id="email" v-model="email" type="email" class="form-control"
                      :class="{ 'is-invalid': emailError }" placeholder="e.g., <EMAIL>" required
                      autocomplete="email" @blur="validateEmail" />
                    <div v-if="emailError" class="invalid-feedback">
                      {{ emailError }}
                    </div>
                  </div>
                  <div class="mb-3">
                    <label for="phone" class="form-label">Phone Number</label>
                    <input id="phone" v-model="phone" type="tel" class="form-control"
                      placeholder="e.g., +****************" required autocomplete="tel" />
                  </div>
                </div>

                <!-- Step 3: Password -->
                <div v-if="currentStep === 3">
                  <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                      <input id="password" v-model="password" :type="showPassword ? 'text' : 'password'"
                        class="form-control" placeholder="Enter a strong password" required
                        autocomplete="new-password" />
                      <button class="btn btn-outline-secondary" type="button" @click="showPassword = !showPassword"
                        aria-label="Toggle password visibility">
                        {{ showPassword ? 'Hide' : 'Show' }}
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Add Robot Protection before the submit button -->
                <RobotProtection @validation="handleRobotValidation" />

                <!-- Navigation Buttons -->
                <div class="d-flex justify-content-between mt-4">
                  <button v-if="currentStep > 1" type="button" class="btn btn-secondary" @click="prevStep">
                    Back
                  </button>
                  <div class="ms-auto">
                    <button v-if="currentStep < totalSteps" type="button" class="btn btn-primary" @click="nextStep">
                      Next
                    </button>
                    <button
                      v-if="currentStep === totalSteps"
                      type="submit"
                      class="btn btn-primary w-100"
                      :disabled="isLoading || !isRobotValid"
                    >
                      <span v-if="isLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      <span v-if="!isLoading">Create Account</span>
                    </button>
                  </div>
                </div>
              </form>

              <!-- Progress Indicator Dots -->
              <div class="d-flex justify-content-center mt-4">
                <span v-for="n in totalSteps" :key="n" class="dot mx-1" :class="{ active: currentStep === n }"></span>
              </div>

              <!-- Secondary CTA: Login -->
              <div class="text-center mt-4">
                <p>Already have an account?</p>
                <button class="btn btn-outline-secondary" @click="goToLogin">Login Here</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Confirmation Modal -->
      <div v-if="showModal" class="modal fade show themed-modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
        aria-modal="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="modalLabel">Registration Successful</h5>
              <button type="button" class="modal-close-btn" aria-label="Close" @click="showModal = false">
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
            <div class="modal-body">
              Your account has been successfully created. Please <strong>login</strong> to access your dashboard.
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-primary" @click="goToLogin">Go to Login</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import apiClient from "@/services/apiClient";
import { useToast } from "vue-toastification"; // ✅ Import toast function
import { ref, onMounted } from "vue";
import RobotProtection from '@/components/RobotProtection.vue';

export default {
  components: {
    RobotProtection
  },
  setup() {
    const toast = useToast(); // ✅ Get the toast instance
    const theme = ref(localStorage.getItem("theme") || "light");

    const toggleTheme = () => {
      theme.value = theme.value === "dark" ? "light" : "dark";
      document.documentElement.setAttribute("data-bs-theme", theme.value);
      localStorage.setItem("theme", theme.value);
    };

    onMounted(() => {
      document.documentElement.setAttribute("data-bs-theme", theme.value);
    });

    return { toast, theme, toggleTheme };
  },
  data() {
    return {
      currentStep: 1,
      totalSteps: 3,
      accountType: 'client',
      companyName: '',
      address: '',
      managerId: '',
      contactName: '',
      email: '',
      password: '',
      taxId: '',
      phone: '',
      emailError: null,
      showPassword: false,
      showModal: false,
      errorMessage: null,
      errorDetails: null,
      isLoading: false,
      showDetails: false,
      isRobotValid: false
    };
  },
  computed: {
    headerText() {
      // Capitalise the account type for display
      const type = this.accountType.charAt(0).toUpperCase() + this.accountType.slice(1);
      return `Registration – ${type}`;
    },
    showDetailsButton() {
      return this.errorDetails !== null;
    }
  },
  methods: {
    selectAccountType(type) {
      this.accountType = type;
    },
    validateEmail() {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      this.emailError = emailRegex.test(this.email) ? null : 'Invalid email format';
    },
    // Validates the current step and sets an error message if validation fails
    validateStep() {
      this.errorMessage = null; // Clear any previous error
      if (this.currentStep === 1) {
        if (this.accountType === 'manager') {
          if (!this.companyName.trim() || !this.address.trim()) {
            this.errorMessage = 'Please fill in the required company details.';
            return false;
          }
        }
        // For client accounts, the managerId is optional—no required fields on Step 1.
      } else if (this.currentStep === 2) {
        if (!this.contactName.trim() || !this.email.trim() || !this.phone.trim()) {
          this.errorMessage = 'Please fill in all required personal details.';
          return false;
        }
        this.validateEmail();
        if (this.emailError) {
          this.errorMessage = 'Please provide a valid email address.';
          return false;
        }
      } else if (this.currentStep === 3) {
        if (!this.password.trim()) {
          this.errorMessage = 'Please provide a password.';
          return false;
        }
      }
      return true;
    },
    nextStep() {
      if (!this.validateStep()) return;
      if (this.currentStep < this.totalSteps) {
        this.currentStep++;
      }
    },
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
        this.errorMessage = null; // Clear errors when moving back
      }
    },
    handleRobotValidation(isValid) {
      this.isRobotValid = isValid;
    },
    async handleSubmit() {
      if (!this.validateStep()) return;
      if (!this.isRobotValid) {
        this.errorMessage = 'Please complete the security check';
        return;
      }
      this.isLoading = true;
      this.errorMessage = null;
      this.errorDetails = null;

      const payload = {
        contactName: this.contactName,
        email: this.email,
        password: this.password,
        taxId: this.taxId || null, // ✅ Ensure null is passed if empty
        phone: this.phone,
        role: this.accountType,
      };

      if (this.accountType === "manager") {
        payload.companyName = this.companyName;
        payload.address = this.address;
      } else if (this.accountType === "client" && this.managerId) {
        payload.managerId = this.managerId;
      }

      try {
        const res = await apiClient.post('/auth/register', payload);

        if (res.ok && res.data.token) {
          localStorage.setItem("token", res.data.token);
          localStorage.setItem("user", JSON.stringify(res.data.user));
          this.toast.success("Registration successful! Please log in.");
          setTimeout(() => {
            this.$router.push('/auth/login');
          }, 1500);
        }
      } catch (error) {
        const status = error.response?.status;
        const serverMessage = error.response?.data?.error;
        const errorDetail = error.response?.data?.detail || "";

        if (
          (serverMessage && serverMessage.toLowerCase().includes("database error")) &&
          errorDetail.toLowerCase().includes("already exists")
        ) {
          this.errorMessage = "An account with this email already exists. Please use a different email address or log in.";
        } else if (errorDetail.toLowerCase().includes("already exists")) {
          this.errorMessage = "An account with this email already exists. Please use a different email address or log in.";
        } else {
          this.errorMessage = serverMessage || "An unexpected error occurred. Please try again.";
        }

        this.errorDetails = `
      Status: ${status || "N/A"}
      URL: ${error.config?.url || "N/A"}
      Method: ${error.config?.method || "N/A"}
      Message: ${errorDetail || error.message || "N/A"}
    `;

        console.error("Registration error:", this.errorDetails);
        // ✅ Use toast for errors
        this.toast.error(this.errorMessage || "An unexpected error occurred. Please try again.");
      } finally {
        this.isLoading = false;
      }
    },
    goToLogin() {
      this.$router.push('/auth/login');
    },
  },
};
</script>


<style lang="scss" scoped>
.register-page {
  min-height: 100vh;
  padding-top: 70px; /* Account for fixed header */
  background-color: var(--bs-body-bg);
  color: var(--bs-body-color);
}

#app {
  > {
    div {
      grid-column: 1/3;
      width: 100%;
      height: 100%;
    }
  }
}

/* Card styling with theme support */
.register-card {
  background-color: var(--bs-card2-bg) !important;
  border: 1px solid var(--bs-border-color) !important;
  box-shadow: 0 8px 30px rgba(var(--bs-dark-rgb), 0.1);
  transition: all 0.3s ease;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

/* Form controls with theme support */
.form-control, .form-select {
  background-color: var(--bs-body-bg);
  border: 1px solid var(--bs-border-color);
  color: var(--bs-body-color);
  transition: all 0.2s ease;

  &:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    background-color: var(--bs-body-bg);
  }

  &::placeholder {
    color: var(--bs-body-color);
    opacity: 0.5;
  }
}

.form-floating {
  .form-control {
    border-radius: 0.5rem;
    padding: 1rem 0.75rem;
    height: auto;
    min-height: 3.5rem;
    background-color: var(--bs-body-bg);
    border: 1px solid var(--bs-border-color);
    color: var(--bs-body-color);
    transition: all 0.2s ease;

    &:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    }
  }

  .input-group {
    .form-control {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .btn {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

/* Button styling */
.btn {
  height: 3.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(var(--bs-dark-rgb), 0.1);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

/* Progress dots */
.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--bs-border-color);
  transition: all 0.3s ease;

  &.active {
    background: var(--bs-primary);
    transform: scale(1.2);
  }
}

/* Modal theming */
.themed-modal {
  background-color: var(--bs-secondary-bg);
  backdrop-filter: blur(5px);

  .modal-content {
    background-color: var(--bs-card2-bg);
    color: var(--bs-body-color);
    border: 1px solid var(--bs-border-color);
  }

  .modal-header, .modal-footer {
    border-color: var(--bs-border-color);
  }

  .modal-close-btn {
    background: none;
    border: none;
    color: var(--bs-body-color);
    opacity: 0.7;
    transition: all 0.2s ease;
    padding: 0.5rem;
    margin: -0.5rem -0.5rem -0.5rem auto;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;

    i {
      font-size: 1.25rem;
    }

    &:hover {
      opacity: 1;
      background-color: rgba(var(--bs-dark-rgb), 0.05);
    }

    [data-bs-theme="dark"] & {
      color: var(--bs-light);
    }
  }
}

/* Optional manager link section */
.optional-manager-link {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  border: 1px solid rgba(var(--bs-primary-rgb), 0.1);
  border-radius: 0.5rem;
  padding: 1.5rem;
}

// Animations
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
