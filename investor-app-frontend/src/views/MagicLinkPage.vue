<template>
  <div class="container">
    <div class="row justify-content-center align-items-center min-vh-100">
      <div class="col-md-6 col-lg-4">
        <div class="card shadow">
          <div class="card-body p-4">
            <h2 class="text-center mb-4">Validating Magic Link</h2>

            <div v-if="isLoading" class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-3">Validating your magic link...</p>
            </div>

            <div v-else-if="error" class="text-center">
              <div class="alert alert-danger">
                {{ error }}
              </div>
              <button class="btn btn-primary" @click="goToLogin">
                Return to Login
              </button>
            </div>

            <div v-else class="text-center">
              <div class="alert alert-success">
                Magic link validated successfully!
              </div>
              <p>Redirecting you to the dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import apiClient from '@/services/apiClient';

export default {
  name: 'MagicLinkPage',
  setup() {
    const router = useRouter();
    const route = useRoute();
    const authStore = useAuthStore();
    const isLoading = ref(true);
    const error = ref(null);

    const validateMagicLink = async () => {
      try {
        const token = route.query.token;
        if (!token) {
          throw new Error('No magic link token provided');
        }

        const res = await apiClient.post('/auth/magic-link/validate', { token });

        if (res.ok && res.data.success) {
          // Store authentication data with rememberMe set to true for magic links
          // This ensures the session lasts for 24 hours
          localStorage.setItem('rememberMe', 'true');
          authStore.setAuthData(res.data.token, res.data.user);

          // Update the rememberMe state in the auth store
          authStore.rememberMe = true;

          // Redirect to dashboard after a short delay
          setTimeout(() => {
            router.push('/dashboard');
          }, 1500);
        } else {
          throw new Error(res.errors?.[0] || res.data.message || 'Failed to validate magic link');
        }
      } catch (err) {
        error.value = err.response?.data?.message || err.message || 'Failed to validate magic link';
        isLoading.value = false;
      }
    };

    const goToLogin = () => {
      router.push('/auth/login');
    };

    onMounted(() => {
      validateMagicLink();
    });

    return {
      isLoading,
      error,
      goToLogin
    };
  }
};
</script>

<style scoped>
.card {
  border: none;
  border-radius: 1rem;
}

.alert {
  border-radius: 0.5rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}
</style>
