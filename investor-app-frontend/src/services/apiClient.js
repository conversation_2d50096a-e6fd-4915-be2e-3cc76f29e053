import axios from 'axios';

// Create an axios instance with a base URL from environment variables
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

// Automatically attach auth token if present
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

function normalize(data) {
  return {
    data: data?.data ?? data,
    warnings: data?.warnings ?? [],
    message: data?.message,
    errors: data?.errors ?? (data?.error ? [data.error] : []),
  };
}

async function request(method, url, options = {}) {
  try {
    const response = await api({ method, url, ...options });
    const normalized = normalize(response.data);
    return { ok: true, status: response.status, ...normalized };
  } catch (err) {
    const response = err.response || {};
    const normalized = normalize(response.data || {});
    if (!normalized.errors.length) {
      normalized.errors = [err.message];
    }
    return { ok: false, status: response.status, ...normalized };
  }
}

const apiClient = {
  get(url, options) {
    return request('get', url, options);
  },
  post(url, data, options = {}) {
    return request('post', url, { ...options, data });
  },
  put(url, data, options = {}) {
    return request('put', url, { ...options, data });
  },
  delete(url, options) {
    return request('delete', url, options);
  },
  async multiStage(stages) {
    const results = [];
    for (const stage of stages) {
      const res = await request(stage.method, stage.url, stage);
      results.push(res);
      if (!res.ok) break;
    }
    return results;
  },
};

export default apiClient;
