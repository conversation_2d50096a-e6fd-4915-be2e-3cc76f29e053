<template>
  <div class="vesting-details">
    <div class="vesting-header">
      <h3>Vesting Schedule</h3>
      <div class="vesting-status">
        <span :class="['status-badge', vestingSchedule?.status]">
          {{ vestingSchedule?.status || 'Loading...' }}
        </span>
      </div>
    </div>

    <div v-if="vestingSchedule" class="vesting-info">
      <div class="info-grid">
        <div class="info-card">
          <label>Start Date</label>
          <p>{{ vestingSchedule.start_date ? formatDate(vestingSchedule.start_date) : 'N/A' }}</p>
        </div>
        <div class="info-card">
          <label>Duration</label>
          <p>{{ vestingSchedule.schedule?.duration || 'N/A' }} days</p>
        </div>
        <div class="info-card">
          <label>Cliff Period</label>
          <p>{{ vestingSchedule.schedule?.cliff_period || 'N/A' }} days</p>
        </div>
        <div class="info-card">
          <label>Release Frequency</label>
          <p>{{ vestingSchedule.schedule?.release_frequency || 'N/A' }}</p>
        </div>
      </div>

      <div class="progress-section">
        <div class="progress-header">
          <h4>Vesting Progress</h4>
          <span class="progress-percentage">
            {{ vestingSchedule.schedule?.current_progress?.vested_percentage?.toFixed(1) || '0' }}%
          </span>
        </div>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: `${vestingSchedule.schedule?.current_progress?.vested_percentage || 0}%` }"
          ></div>
        </div>
        <div class="progress-details">
          <span>Elapsed: {{ vestingSchedule.schedule?.current_progress?.elapsed_days || 0 }} days</span>
          <span>Remaining: {{ vestingSchedule.schedule?.current_progress?.remaining_days || 0 }} days</span>
        </div>
      </div>

      <div v-if="chartData && chartData.length > 0" class="chart-section">
        <h4>Vesting Timeline</h4>
        <div class="chart-container">
          <LineChart :data="chartData" />
        </div>
      </div>

      <div class="beneficiaries-section">
        <h4>Beneficiaries</h4>
        <div class="beneficiaries-list">
          <div v-if="vestingSchedule.beneficiaries && vestingSchedule.beneficiaries.length > 0">
            <div v-for="beneficiary in vestingSchedule.beneficiaries"
                 :key="beneficiary.id"
                 class="beneficiary-card">
              <div class="beneficiary-info">
                <span class="beneficiary-name">{{ beneficiary.name || 'Unknown' }}</span>
                <span class="beneficiary-percentage">{{ beneficiary.percentage || 0 }}%</span>
              </div>
              <div class="beneficiary-amounts">
                <div class="amount-item">
                  <label>Vested Amount</label>
                  <span>${{ formatNumber(beneficiary.vested_amount || 0) }}</span>
                </div>
                <div class="amount-item">
                  <label>Total Allocation</label>
                  <span>${{ formatNumber(((vestingSchedule.asset?.value || 0) * (beneficiary.percentage || 0)) / 100) }}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-beneficiaries">
            <p>No beneficiaries found</p>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="loading-state">
      <div class="spinner"></div>
      <p>Loading vesting details...</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue';
import { useToast } from 'vue-toastification';
import apiClient from '@/services/apiClient';
import {
  Chart,
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  TimeScale,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import 'chartjs-adapter-date-fns';
import { format } from 'date-fns';

// Register Chart.js components
Chart.register(
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  TimeScale,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default {
  props: {
    contract: {
      type: Object,
      required: true
    },
    isManager: {
      type: Boolean,
      default: false
    }
  },
  emits: ['status-updated'],
  setup(props, { emit }) {
    const toast = useToast();
    const vestingChart = ref(null);
    const chartInstance = ref(null);
    const vestingSchedule = ref(null);
    const chartData = ref([]);

    const shortenHash = (hash) => {
      if (!hash) return '';
      return `${hash.substring(0, 5)}...${hash.substring(hash.length - 5)}`;
    };

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const formatDuration = (days) => {
      if (!days) return 'N/A';
      if (days < 365) return `${days} days`;
      const years = Math.floor(days / 365);
      const remainingDays = days % 365;
      return `${years} year${years > 1 ? 's' : ''}${remainingDays ? ` ${remainingDays} days` : ''}`;
    };

    const getStatusClass = (status) => ({
      active: 'text-success',
      paused: 'text-warning',
      terminated: 'text-danger'
    }[status] || 'text-muted');

    const getVestingProgress = () => {
      const startDate = new Date(props.contract.vesting_schedule.start_date);
      const now = new Date();
      const duration = props.contract.vesting_schedule.duration * 24 * 60 * 60 * 1000; // Convert days to milliseconds
      const elapsed = now - startDate;
      const progress = Math.min(100, (elapsed / duration) * 100);
      return Math.round(progress);
    };

    const calculateVestedAmount = (beneficiary) => {
      const progress = getVestingProgress() / 100;
      const totalAmount = props.contract.asset_value * (beneficiary.percentage / 100);
      return (totalAmount * progress).toLocaleString();
    };

    const getBeneficiaryStatus = (beneficiary) => {
      const progress = getVestingProgress();
      if (progress === 0) return 'Not Started';
      if (progress === 100) return 'Fully Vested';
      return 'Vesting in Progress';
    };

    const getBeneficiaryStatusClass = (beneficiary) => {
      const status = getBeneficiaryStatus(beneficiary);
      return {
        'Not Started': 'text-muted',
        'Vesting in Progress': 'text-warning',
        'Fully Vested': 'text-success'
      }[status] || 'text-muted';
    };

    const updateStatus = async (newStatus) => {
      try {
        await apiClient.patch(`/vesting/${props.contract.id}/status`, { status: newStatus });
        toast.success(`Vesting contract ${newStatus} successfully`);
        emit('status-updated', newStatus);
      } catch (error) {
        toast.error(error.response?.data?.error || 'Failed to update vesting status');
      }
    };

    const fetchVestingSchedule = async () => {
      try {
        console.log('Fetching vesting schedule for asset:', props.contract.asset_id);
        const response = await apiClient.get(`/vesting/${props.contract.asset_id}/schedule`);
        console.log('Raw API response:', response);
        console.log('Response data:', response.data);

        if (!response.data) {
          console.error('No data received from API');
          toast.error('No data received from server');
          return;
        }

        if (!response.data.vesting) {
          console.error('No vesting data in response:', response.data);
          toast.error('Invalid vesting data received');
          return;
        }

        vestingSchedule.value = response.data.vesting;
        console.log('Updated vestingSchedule:', vestingSchedule.value);

        // Calculate chart data points
        const schedule = response.data.vesting.schedule;
        if (!schedule) {
          console.error('No schedule data in vesting:', response.data.vesting);
          return;
        }

        if (!schedule.start_date) {
          console.error('No start date in schedule:', schedule);
          return;
        }

        const startDate = new Date(schedule.start_date);
        const totalDays = schedule.duration || 0;
        const cliffDays = schedule.cliff_period || 0;

        // Generate data points for the chart
        const dataPoints = [];
        for (let day = 0; day <= totalDays; day += 7) { // Weekly points
          const date = new Date(startDate);
          date.setDate(date.getDate() + day);

          let vestedPercentage = 0;
          if (day >= totalDays) {
            vestedPercentage = 100;
          } else if (day > cliffDays) {
            vestedPercentage = Math.min(100, ((day - cliffDays) / (totalDays - cliffDays)) * 100);
          }

          dataPoints.push({
            date: date.toISOString().split('T')[0],
            vested: vestedPercentage,
            unvested: 100 - vestedPercentage
          });
        }

        console.log('Generated chart data points:', dataPoints);
        chartData.value = dataPoints;

      } catch (error) {
        console.error('Error fetching vesting schedule:', error);
        console.error('Error details:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status
        });
        toast.error('Failed to fetch vesting schedule');
      }
    };

    const renderChart = () => {
      if (!vestingChart.value) return;

      const { dataPoints, startDate, endDate, cliffDate } = calculateVestingData();
      const totalAmount = props.contract.asset_value;

      // Destroy existing chart if it exists
      if (chartInstance.value) {
        chartInstance.value.destroy();
        chartInstance.value = null;
      }

      // Ensure the canvas is ready
      const ctx = vestingChart.value.getContext('2d');
      if (!ctx) return;

      // Create new chart
      chartInstance.value = new Chart(ctx, {
        type: 'line',
        data: {
          datasets: [
            {
              label: 'Vested Amount',
              data: dataPoints,
              borderColor: 'rgb(99, 102, 241)',
              backgroundColor: 'rgba(99, 102, 241, 0.1)',
              fill: true,
              tension: 0.4,
              pointRadius: 0,
              pointHoverRadius: 4,
              pointHoverBackgroundColor: 'rgb(99, 102, 241)',
              pointHoverBorderColor: '#fff',
              pointHoverBorderWidth: 2,
              pointHoverShadowBlur: 10,
              pointHoverShadowColor: 'rgba(99, 102, 241, 0.5)',
              pointHoverShadowOffsetX: 0,
              pointHoverShadowOffsetY: 0
            },
            {
              label: 'Total Amount',
              data: [
                { x: startDate, y: 0 },
                { x: endDate, y: totalAmount }
              ],
              borderColor: 'rgba(99, 102, 241, 0.3)',
              borderDash: [5, 5],
              fill: false,
              pointRadius: 0
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: {
              left: 0,
              right: 0
            }
          },
          animation: {
            duration: 1000,
            easing: 'easeInOutQuart'
          },
          plugins: {
            title: { display: false },
            tooltip: {
              mode: 'index',
              intersect: false,
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              titleColor: '#1f2937',
              bodyColor: '#4b5563',
              borderColor: '#e5e7eb',
              borderWidth: 1,
              padding: 12,
              displayColors: true,
              boxPadding: 4,
              usePointStyle: true,
              callbacks: {
                label: function(context) {
                  return `${context.dataset.label}: $${context.parsed.y.toLocaleString()}`;
                },
                title: function(context) {
                  return format(new Date(context[0].parsed.x), 'MMM d, yyyy');
                }
              }
            },
            legend: {
              display: true,
              position: 'bottom',
              align: 'start',
              labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                  size: 12
                },
                boxWidth: 8,
                boxHeight: 8
              }
            }
          },
          scales: {
            x: {
              type: 'time',
              time: {
                tooltipFormat: 'PPpp',
                unit: 'day'
              },
              grid: {
                display: false,
                drawBorder: false
              },
              ticks: {
                maxRotation: 0,
                color: '#6b7280',
                font: {
                  size: 12
                },
                padding: 8
              },
              adapters: {
                date: { format }
              },
              bounds: 'data',
              offset: false
            },
            y: {
              grid: {
                color: 'rgba(0, 0, 0, 0.05)',
                drawBorder: false
              },
              ticks: {
                color: '#6b7280',
                font: {
                  size: 12
                },
                padding: 8,
                callback: function(value) {
                  return '$' + value.toLocaleString();
                }
              },
              bounds: 'data',
              offset: false
            }
          },
          interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
          }
        }
      });
    };

    // Call fetchVestingSchedule when component is mounted
    onMounted(() => {
      if (props.contract?.asset_id) {
        fetchVestingSchedule();
      }
    });

    // Watch for contract changes
    watch(() => props.contract?.asset_id, (newAssetId) => {
      if (newAssetId) {
        fetchVestingSchedule();
      }
    });

    // Watch for contract changes and re-render chart
    watch(() => props.contract, () => {
      nextTick(() => {
        renderChart();
      });
    }, { deep: true });

    onMounted(() => {
      nextTick(() => {
        renderChart();
      });
    });

    // Cleanup on component unmount
    onUnmounted(() => {
      if (chartInstance.value) {
        chartInstance.value.destroy();
        chartInstance.value = null;
      }
    });

    return {
      vestingSchedule,
      chartData,
      vestingChart,
      shortenHash,
      formatDate,
      formatDuration,
      getStatusClass,
      getVestingProgress,
      calculateVestedAmount,
      getBeneficiaryStatus,
      getBeneficiaryStatusClass,
      updateStatus,
      fetchVestingSchedule
    };
  }
};
</script>

<style scoped lang="scss">
.vesting-details {
  .vesting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--bs-primary);
    }

    .vesting-status {
      .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }

  .vesting-info {
    .info-grid {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1.5rem;

      .info-card {
        background: rgba(var(--bs-primary-rgb), 0.05);
        border-radius: 8px;
        padding: 1rem;
        height: 100%;

        label {
          font-size: 0.875rem;
          color: var(--bs-muted);
          margin-bottom: 0.25rem;
        }

        p {
          font-size: 1rem;
          font-weight: 500;
          margin: 0;
        }
      }
    }

    .progress-section {
      margin-bottom: 1.5rem;

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        h4 {
          font-size: 1rem;
          font-weight: 600;
          color: var(--bs-primary);
        }

        .progress-percentage {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--bs-primary);
        }
      }

      .progress-bar {
        height: 1.5rem;
        background-color: rgba(var(--bs-primary-rgb), 0.1);
        border-radius: 1rem;
        overflow: hidden;

        .progress-fill {
          background-color: var(--bs-primary);
          transition: width 0.3s ease;
        }
      }

      .progress-details {
        display: flex;
        justify-content: space-between;
        margin-top: 0.5rem;

        span {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--bs-muted);
        }
      }
    }

    .chart-section {
      margin-bottom: 1.5rem;

      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--bs-primary);
        margin-bottom: 0.5rem;
      }

      .chart-container {
        position: relative;
        width: 100%;
        margin: 0 -1.5rem 2rem -1.5rem;
        background: var(--bs-body-bg);
        border-radius: 0;
        padding: 1.5rem;
        border: none;
        border-bottom: 1px solid var(--bs-border-color);
        border-top: 1px solid var(--bs-border-color);

        @media (max-width: 768px) {
          margin: 0 -1rem 1.5rem -1rem;
          padding: 1rem;
          height: 250px;
        }
      }
    }

    .beneficiaries-section {
      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--bs-primary);
        margin-bottom: 0.5rem;
      }

      .beneficiaries-list {
        .beneficiary-card {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem;
          background: rgba(var(--bs-primary-rgb), 0.05);
          border-radius: 8px;
          margin-bottom: 0.5rem;

          .beneficiary-info {
            .beneficiary-name {
              font-size: 1rem;
              font-weight: 500;
              color: var(--bs-primary);
            }

            .beneficiary-percentage {
              font-size: 0.875rem;
              font-weight: 500;
              color: var(--bs-muted);
            }
          }

          .beneficiary-amounts {
            .amount-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 0.5rem;

              label {
                font-size: 0.875rem;
                font-weight: 500;
                color: var(--bs-muted);
              }

              span {
                font-size: 1rem;
                font-weight: 500;
                color: var(--bs-primary);
              }
            }
          }
        }
      }
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(var(--bs-primary-rgb), 0.2);
      border-top-color: var(--bs-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    }

    p {
      font-size: 1rem;
      font-weight: 500;
      color: var(--bs-muted);
    }
  }
}
</style>
