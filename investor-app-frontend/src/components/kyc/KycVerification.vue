<template>
  <div class="kyc-verification">
    <div v-if="loading" class="loading-overlay">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Verifying...</span>
      </div>
      <p class="mt-2">Verifying your identity...</p>
    </div>

    <div class="verification-steps" :class="{ 'opacity-50': loading }">
      <!-- Step 1: Passport Upload -->
      <div class="step mb-4">
        <h5>Step 1: Upload Passport</h5>
        <div class="upload-area"
             :class="{ 'is-invalid': errors.passport }"
             @click="triggerPassportUpload">
          <input type="file"
                 ref="passportInput"
                 accept="image/*"
                 @change="handlePassportUpload"
                 class="d-none">
          <div class="upload-content">
            <i class="bi bi-passport display-4"></i>
            <p class="mt-2 mb-0">Click to upload passport photo</p>
            <small class="text-muted">Supported formats: JPG, PNG</small>
          </div>
        </div>
        <div v-if="passportPreview" class="mt-2">
          <img :src="passportPreview" class="img-thumbnail" style="max-height: 200px;">
          <button class="btn btn-sm btn-outline-danger ms-2" @click="removePassport">
            Remove
          </button>
        </div>
        <div v-if="errors.passport" class="invalid-feedback">
          {{ errors.passport }}
        </div>
      </div>

      <!-- Step 2: Selfie Upload -->
      <div class="step mb-4">
        <h5>Step 2: Take a Selfie</h5>
        <div class="upload-area"
             :class="{ 'is-invalid': errors.selfie }"
             @click="triggerSelfieUpload">
          <input type="file"
                 ref="selfieInput"
                 accept="image/*"
                 @change="handleSelfieUpload"
                 class="d-none">
          <div class="upload-content">
            <i class="bi bi-camera display-4"></i>
            <p class="mt-2 mb-0">Click to upload selfie</p>
            <small class="text-muted">Supported formats: JPG, PNG</small>
          </div>
        </div>
        <div v-if="selfiePreview" class="mt-2">
          <img :src="selfiePreview" class="img-thumbnail" style="max-height: 200px;">
          <button class="btn btn-sm btn-outline-danger ms-2" @click="removeSelfie">
            Remove
          </button>
        </div>
        <div v-if="errors.selfie" class="invalid-feedback">
          {{ errors.selfie }}
        </div>
      </div>

      <!-- Submit Button -->
      <div class="text-center mt-4">
        <button class="btn btn-primary"
                @click="submitVerification"
                :disabled="!canSubmit || loading">
          {{ loading ? 'Verifying...' : 'Submit Verification' }}
        </button>
      </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade"
         id="successModal"
         tabindex="-1"
         ref="successModal">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Verification Submitted</h5>
            <button type="button"
                    class="modal-close-btn"
                    data-bs-dismiss="modal">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body text-center">
            <i class="bi bi-check-circle-fill text-success display-1"></i>
            <p class="mt-3">Your verification request has been submitted successfully.</p>
            <p class="text-muted">We'll notify you once the verification is complete.</p>
          </div>
          <div class="modal-footer">
            <button type="button"
                    class="btn btn-primary"
                    data-bs-dismiss="modal"
                    @click="handleSuccess">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Modal -->
    <div class="modal fade"
         id="errorModal"
         tabindex="-1"
         ref="errorModal">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Verification Failed</h5>
            <button type="button"
                    class="modal-close-btn"
                    data-bs-dismiss="modal">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body text-center">
            <i class="bi bi-x-circle-fill text-danger display-1"></i>
            <p class="mt-3">{{ errorMessage }}</p>
          </div>
          <div class="modal-footer">
            <button type="button"
                    class="btn btn-primary"
                    data-bs-dismiss="modal">
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Modal } from 'bootstrap';
import apiClient from '@/services/apiClient';

const emit = defineEmits(['verification-submitted']);

const loading = ref(false);
const errorMessage = ref('');
const passportFile = ref(null);
const selfieFile = ref(null);
const passportPreview = ref(null);
const selfiePreview = ref(null);
const errors = ref({
  passport: null,
  selfie: null
});

const passportInput = ref(null);
const selfieInput = ref(null);
const successModal = ref(null);
const errorModal = ref(null);

const canSubmit = computed(() => {
  return passportFile.value && selfieFile.value && !loading.value;
});

const triggerPassportUpload = () => {
  passportInput.value.click();
};

const triggerSelfieUpload = () => {
  selfieInput.value.click();
};

const handlePassportUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    validateFile(file, 'passport');
  }
};

const handleSelfieUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    validateFile(file, 'selfie');
  }
};

const validateFile = (file, type) => {
  // Reset error
  errors.value[type] = null;

  // Check file type
  if (!file.type.startsWith('image/')) {
    errors.value[type] = 'Please upload an image file';
    return;
  }

  // Check file size (5MB limit)
  if (file.size > 5 * 1024 * 1024) {
    errors.value[type] = 'File size must be less than 5MB';
    return;
  }

  // Create preview
  const reader = new FileReader();
  reader.onload = (e) => {
    if (type === 'passport') {
      passportFile.value = file;
      passportPreview.value = e.target.result;
    } else {
      selfieFile.value = file;
      selfiePreview.value = e.target.result;
    }
  };
  reader.readAsDataURL(file);
};

const removePassport = () => {
  passportFile.value = null;
  passportPreview.value = null;
  errors.value.passport = null;
};

const removeSelfie = () => {
  selfieFile.value = null;
  selfiePreview.value = null;
  errors.value.selfie = null;
};

const submitVerification = async () => {
  try {
    loading.value = true;
    errorMessage.value = '';

    const formData = new FormData();
    formData.append('passport', passportFile.value);
    formData.append('selfie', selfieFile.value);

    const res = await apiClient.post('/kyc/verify', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    if (res.ok && res.data.success) {
      const modal = new Modal(successModal.value);
      modal.show();
      emit('verification-submitted');
    } else {
      throw new Error(res.errors?.[0] || res.data.message || 'Verification failed');
    }
  } catch (error) {
    errorMessage.value = error.response?.data?.message || error.message || 'An error occurred during verification';
    const modal = new Modal(errorModal.value);
    modal.show();
  } finally {
    loading.value = false;
  }
};

const handleSuccess = () => {
  emit('verification-submitted');
  // Reset form
  passportFile.value = null;
  selfieFile.value = null;
  passportPreview.value = null;
  selfiePreview.value = null;
  errors.value = {
    passport: null,
    selfie: null
  };
};
</script>

<style scoped>
.kyc-verification {
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 0.5rem;
}

.opacity-50 {
  opacity: 0.5;
  pointer-events: none;
}

.upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.upload-area.is-invalid {
  border-color: var(--bs-danger);
}

.upload-content {
  color: #6c757d;
}

.upload-content i {
  color: var(--bs-primary);
}

.step {
  background-color: var(--bs-card2-bg);
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.step h5 {
  margin-bottom: 1rem;
  color: var(--bs-primary);
}
</style>
