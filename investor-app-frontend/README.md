# Investor App Frontend

## Overview
A modern Vue.js-based frontend application for the Investor Platform, providing a sophisticated interface for investment management, asset tokenization, and client relationship management.

## Features

### User Interface Components
- **KYC Verification**
  - Live selfie capture with device camera
  - Passport document upload and preview
  - Real-time face detection
  - OCR for passport data extraction
  - Verification status display
  - Progress tracking
  - Mobile-responsive design

- **Navigation**
  - Responsive navigation bar
  - Role-based menu items
  - Real-time notifications dropdown
  - User profile quick access

- **Dashboard**
  - Balance history visualisation with ApexCharts
  - Asset portfolio overview
  - Recent transactions
  - Performance metrics

- **Profile Management**
  - Profile information editing
  - Profile picture upload
  - Account settings management
  - Notification preferences

- **Asset Management**
  - Asset tokenization interface
  - Token transfer functionality
  - Transaction history
  - Portfolio analytics

- **Client Management**
  - Client list view
  - Client profile management
  - Client-manager relationship controls
  - Investment tracking

- **Notifications**
  - Real-time notification system
  - Notification modal
  - Read/unread status management
  - Notification preferences

### Technical Features
- Vue.js 3 with Composition API
- Vite build system
- State management with Pinia
- Vue Router for navigation
- Cypress for E2E testing
- ESL<PERSON> + Prettier for code quality
- Responsive design with modern CSS
- ApexCharts for data visualisation
- vue-web-cam for camera integration
- face-api.js for face detection
- Tesseract.js for OCR

## Project Structure
```
src/
├── assets/          # Static assets
├── components/      # Vue components
│   └── kyc/        # KYC verification components
├── directives/      # Custom Vue directives
├── router/          # Vue Router configuration
├── stores/          # Pinia stores
└── views/           # Page components
```

## Components
- **KycVerification.vue**: Main KYC verification component
- **NavBar.vue**: Main navigation component
- **ProfileTile.vue**: User profile display
- **BalanceHistoryApex.vue**: Balance visualisation
- **NotificationsModal.vue**: Notifications interface
- **TokeniseAssetModal.vue**: Asset tokenization interface
- **ManagerProfileModal.vue**: Manager profile view

## State Management
- Authentication state
- User profile data
- Notification state
- Asset portfolio data
- Client relationships
- KYC verification status

## Router Configuration
- Protected routes
- Role-based access control
- Navigation guards
- Route meta fields

## Development Setup
1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Configure environment variables:
   - Copy .env.example to .env
   - Set required variables
4. Start development server:
   ```bash
   npm run dev
   ```

## Testing
- Unit tests: `npm run test:unit`
- E2E tests: `npm run test:e2e`
- Run all tests for dev and prod: `npm run test:all`

## Build
```bash
npm run build
```

## Code Quality
- ESLint configuration
- Prettier formatting
- Editor config
- Git hooks

## Performance Optimization
- Code splitting
- Lazy loading
- Asset optimization
- Cache management

## Security Features
- XSS protection
- CSRF protection
- Secure HTTP headers
- Input validation

## Browser Support
- Modern browsers
- Mobile responsiveness
- Progressive enhancement
- Fallback support

## Integration
- Backend API integration
- WebSocket connections
- Third-party services
- Authentication flow

## Deployment
- Production build process
- Environment configuration
- Performance optimization
- CDN integration

## Monitoring
- Error tracking
- Performance monitoring
- User analytics
- Debug logging

## Accessibility
- ARIA labels
- Keyboard navigation
- Screen reader support
- Color contrast compliance

## Internationalization
- Multi-language support
- RTL layout support
- Date/time formatting
- Number formatting
