# Investor App Backend

## Overview
This is the backend service for the Investor App platform, providing a robust API for investment management, asset tokenization, and client relationship management.

## Features
- **Authentication & Authorization**
  - JWT-based authentication
  - Role-based access control (Admin, Manager, Client)
  - Secure password hashing and validation

- **Profile Management**
  - User profile creation and updates
  - Profile image upload with Cloudinary integration
  - Multi-currency support for account balances

- **Asset Tokenization**
  - Digital asset creation and management
  - Token issuance and transfer capabilities
  - Real-time asset valuation

- **Client Management**
  - Client onboarding and profile management
  - Investment tracking and reporting
  - Client-manager relationship management

- **Subscription Management**
  - Subscription plan creation and management
  - Automated billing integration
  - Subscription status tracking

- **Notifications System**
  - Real-time notifications
  - Email notifications for important events
  - Customizable notification preferences

- **Exchange Rates**
  - Real-time currency exchange rates
  - Multi-currency support
  - Historical exchange rate tracking

## Technical Features
- Express.js based RESTful API
- MongoDB database integration
- Rate limiting for API protection
- CORS security configuration
- Helmet security middleware
- Winston logging system
- Automated cron jobs for scheduled tasks
- Cloudinary integration for file storage
- Vercel deployment support

## API Endpoints

### Authentication
- POST `/auth/register` - User registration
- POST `/auth/login` - User login
- POST `/auth/refresh-token` - Refresh JWT token

### Profile
- GET `/profile` - Get user profile
- PUT `/profile` - Update user profile
- POST `/profile/upload-image` - Upload profile image

### Subscriptions
- GET `/subscriptions` - List available subscriptions
- POST `/subscriptions` - Create subscription
- PUT `/subscriptions/:id` - Update subscription
- DELETE `/subscriptions/:id` - Cancel subscription

### Tokenisation
- POST `/tokenisation/create` - Create new token
- GET `/tokenisation/assets` - List tokenized assets
- PUT `/tokenisation/transfer` - Transfer tokens
- GET `/tokenisation/history` - View transaction history

### Clients
- GET `/clients` - List clients
- POST `/clients` - Add new client
- PUT `/clients/:id` - Update client details
- DELETE `/clients/:id` - Remove client

### Notifications
- GET `/notifications` - List notifications
- PUT `/notifications/:id` - Mark notification as read
- POST `/notifications/settings` - Update notification preferences

### Exchange Rates
- GET `/exchange-rates/current` - Get current rates
- GET `/exchange-rates/historical` - Get historical rates

## Environment Variables
Required environment variables (see .env.example for details):
- DATABASE_URL
- JWT_SECRET
- CLOUDINARY_*
- CORS_ALLOWED_ORIGIN
- Other service-specific variables

## Setup and Installation
1. Clone the repository
2. Install dependencies: `npm install`
3. Copy .env.example to .env and configure variables
4. Run development server: `npm run dev`
5. Run tests: `npm test`
6. Run API checks: `node scripts/run-api-tests.js [dev|prod]`

The API check script sends requests to key endpoints defined in `tests/endpoints.json` and reports the status of each. Use `dev` (default) to target your local server or `prod` to test the production URL specified in the config.

## Security Features
- JWT authentication with secure token handling
- Rate limiting with IP-based restrictions
  - General API: 100 requests per 15 minutes
  - Authentication: 5 failed attempts per hour
- CORS protection with strict origin validation
- Helmet security headers with CSP configuration
- Secure password hashing with bcrypt
- Input validation and sanitization
  - XSS protection
  - SQL injection prevention
  - Parameter pollution prevention
  - NoSQL query injection protection
- Comprehensive error logging
- Request validation middleware
- Data sanitization utilities
- Secure file upload handling
- Environment variable protection
- Regular security updates and dependency management

### SQL Injection Protection
- All database queries use parameterized statements
- Input sanitization for SQL parameters
- Strict type checking for database inputs
- Prepared statements for all dynamic queries
- Query parameter validation and sanitization

### Additional Security Measures
- Content Security Policy (CSP) implementation
- Cross-Origin Resource Policy (CORP) configuration
- HTTP Parameter Pollution (HPP) prevention
- XSS protection with input sanitization
- Secure cookie handling
- Request size limiting
- Input length restrictions
- Special character escaping
- Regular security audits and updates

## Error Handling
- Standardized error responses
- Detailed logging
- Rate limit notifications
- Graceful error recovery

## Deployment
- Vercel-ready configuration
- Production optimization
- Environment-specific settings
- Automated deployment pipeline

## Monitoring
- Winston logging integration
- Error tracking
- Performance monitoring
- API usage statistics

## Version Control
- Git-based versioning
- Automated version endpoint
- Change tracking

---

## 🚀 Features

### 🔑 Authentication & User Management
- **JWT-based authentication**
- **Role-based access control** (`Client`, `Manager`, `Admin`)
- **User profile management** (update details, change password, profile picture uploads)

### 📊 Asset Tokenisation
- **Managers can tokenise assets** for clients
- **Clients can propose tokenised assets**, requiring manager approval
- **Asset statuses:** `pending`, `approved`, `rejected`
- **Secure blockchain signing** for verifying tokenised assets

### 🔗 Client-Manager Linking
- **Managers can send link requests** to clients
- **Clients can accept, reject, or ignore** link requests
- **Linked clients' assets are visible to their respective managers**

### 🔔 Notifications System
- **Real-time notifications** for link requests and asset approvals
- **Unread notifications counter** in the frontend UI
- **Clients get notifications** when a manager links them or approves an asset

---

## 📦 API Endpoints

- `POST /auth/login` - Authenticate users
- `POST /auth/register` - Register new users
- `GET /profile` - Fetch and update user profile
- `POST /tokenisation/create` - Tokenise new assets
- `PUT /tokenisation/approve/:id` - Approve a proposed asset
- `GET /clients/search` - Search clients (**restricted to managers**)
- `POST /clients/link` - Request to link a client
- `GET /notifications` - Fetch notifications
- `PUT /notifications/mark-read/:id` - Mark a notification as read

_(See API documentation for full list)_

---

## 🛠️ Setup & Installation

### 1️⃣ Prerequisites

Ensure you have the following installed:
- **Node.js** (v18 or later)
- **PostgreSQL** (for database)
- **Docker** (Optional) (for running a local database)

---

### 2️⃣ Clone the Repository

```sh
git clone https://github.com/your-org/investor-app-backend.git
cd investor-app-backend