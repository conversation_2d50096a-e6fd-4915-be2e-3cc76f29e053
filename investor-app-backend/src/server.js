// server.js (Main App Entry Point)

import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import cron from 'node-cron';
import initialiseDatabase from './setup/databases.js';
import { createMessagingTables } from './migrations/create_messaging_tables.js';
import { execSync } from 'child_process';
import compression from 'compression';
import { logger } from './utils/logger.js';
import path from 'path';
import mongoSanitize from 'express-mongo-sanitize';
import xss from 'xss-clean';
import hpp from 'hpp';
import { responseHandler } from './middlewares/response.js';

// Import Routes
import authRoutes from './routes/auth.js';
import profileRoutes from './routes/profile.js';
import subscriptionsRoutes from './routes/subscriptions.js';
import tokenisationRoutes from './routes/tokenisation.js';
import clientsRouter from './routes/clients.js';
import notificationsRoutes from './routes/notifications.js';
import exchangeRatesRoutes from './routes/exchangeRates.js';
import helpRoutes from './routes/help.js';
import newsletterRoutes from './routes/newsletter.js';
import serverless from 'serverless-http';
import cronRoutes from './routes/cron.js';
import activityRoutes from './routes/activity.js';
import kycRoutes from './routes/kyc.js';
import vestingRoutes from './routes/vesting.js';
import reportsRoutes from './routes/reportsRoutes.js';
import messagesRoutes from './routes/messages.js';

// Initialize environment variables
dotenv.config();

// Create Express app
const app = express();

// Version Control
let backendVersion = 'N/A';
try {
    backendVersion = execSync('git rev-parse --short HEAD').toString().trim();
} catch (e) {
    logger.error("Error obtaining Git commit version:", e);
}

// Security Middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "https://api.coingecko.com"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
            sandbox: ['allow-forms', 'allow-scripts', 'allow-same-origin']
        },
    },
    crossOriginEmbedderPolicy: false,
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS Configuration
const corsOptions = {
    origin: process.env.NODE_ENV === 'production'
        ? process.env.CORS_ALLOWED_ORIGIN.split(',').map(origin => origin.trim())
        : true, // Allow all origins in development
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
    exposedHeaders: ['Cross-Origin-Resource-Policy'],
    credentials: true,
    maxAge: 86400, // 24 hours
    preflightContinue: false
};

// Apply CORS before other middleware
app.use(cors(corsOptions));

// Handle preflight requests explicitly
app.options('*', cors(corsOptions));

// Rate Limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
});

// Stricter rate limiting for auth routes
const authLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // Limit each IP to 5 failed login attempts per hour
    message: 'Too many login attempts, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
});

// Middleware
app.use(compression()); // Compress responses
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(limiter);
app.use(responseHandler);

// Data sanitization against NoSQL query injection
app.use(mongoSanitize());

// Data sanitization against XSS
app.use(xss());

// Prevent parameter pollution
app.use(hpp());

// Request Logging Middleware
app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        logger.info({
            method: req.method,
            path: req.path,
            status: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip
        });
    });
    next();
});

// Version Endpoint
app.get('/version', (req, res) => {
    res.json({
        version: backendVersion,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
    });
});

// Health Check Endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
    });
});

// API Routes
app.use('/cron', cronRoutes);
app.use('/exchange-rates', exchangeRatesRoutes);
app.use('/auth', authRoutes);
app.use('/profile', profileRoutes);
app.use('/subscriptions', subscriptionsRoutes);
app.use('/tokenisation', tokenisationRoutes);
app.use('/clients', clientsRouter);
app.use('/notifications', notificationsRoutes);
app.use('/help', helpRoutes);
app.use('/newsletter', newsletterRoutes);
app.use('/activity', activityRoutes);
app.use('/kyc', kycRoutes);
app.use('/vesting', vestingRoutes);
app.use('/reports', reportsRoutes);
app.use('/messages', messagesRoutes);

// Static File Serving with Security Headers
app.use('/uploads', express.static('uploads', {
    setHeaders: (res, path, stat) => {
        res.setHeader('Cross-Origin-Resource-Policy', 'same-site');
        res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.setHeader('X-Content-Type-Options', 'nosniff');
    },
    maxAge: 0
}));

// 404 Handler
app.use((req, res) => {
    res.fail({ statusCode: 404, message: 'Route not found', errors: [{ message: 'Route not found' }] });
});

// Global Error Handler
app.use((err, req, res, next) => {
    logger.error({
        error: err.message,
        stack: process.env.NODE_ENV === 'production' ? undefined : err.stack,
        path: req.path,
        method: req.method,
        ip: req.ip,
        body: req.body,
        files: req.files
    });

    // Handle specific error types
    if (err.name === 'ValidationError') {
        return res.fail({ statusCode: 400, message: err.message, errors: [{ message: err.message }] });
    }
    if (err.name === 'UnauthorizedError') {
        return res.fail({ statusCode: 401, message: 'Unauthorized access', errors: [{ message: 'Unauthorized access' }] });
    }
    if (err.name === 'ForbiddenError') {
        return res.fail({ statusCode: 403, message: 'Access forbidden', errors: [{ message: 'Access forbidden' }] });
    }
    if (err.name === 'MulterError') {
        return res.fail({ statusCode: 400, message: 'File upload error', errors: [{ message: err.message }] });
    }

    // Default error response
    res.fail({
        statusCode: err.status || 500,
        message: process.env.NODE_ENV === 'production' ? 'An unexpected error occurred' : err.message,
        errors: [{ message: err.message }]
    });
});

// Environment-specific Configuration
if (process.env.VERCEL) {
    logger.info("Running on Vercel Platform");
} else {
    logger.info("Running in standard environment");

    // Initialize database
    (async () => {
        try {
            await initialiseDatabase();
            await createMessagingTables();
            logger.info('Database initialized successfully');
        } catch (error) {
            logger.error('Database initialization failed:', error);
            process.exit(1);
        }
    })();

    // Start server
    const PORT = process.env.PORT || 3000;
    app.listen(PORT, () => {
        logger.info(`Server running on http://localhost:${PORT}`);
        logger.info(`Environment: ${process.env.NODE_ENV}`);
        logger.info(`Version: ${backendVersion}`);
    });
}

// Graceful Shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received. Shutting down gracefully...');
    // Close server and database connections
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Export the app for serverless environments
export default app;