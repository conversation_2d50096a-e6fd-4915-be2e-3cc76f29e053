export const responseHandler = (req, res, next) => {
  res.success = ({ data = null, message = '', warnings = [] } = {}) => {
    res.json({ status: 'success', data, errors: [], warnings, message });
  };

  res.fail = ({ statusCode = 400, errors = [], message = '', warnings = [] } = {}) => {
    res.status(statusCode).json({ status: 'error', data: null, errors, warnings, message });
  };

  next();
};
