import express from 'express';
import multer from 'multer';
import { authenticateToken } from '../middlewares/auth.js';
import winston from 'winston';
import pg from 'pg';
import cloudinary from '../cloudinary/cloudinaryConfig.js';
import EmailService from '../services/emailService.js';

const { Pool } = pg;
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

const router = express.Router();
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
    ),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'error.log', level: 'error' })
    ]
});

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
        files: 3 // Maximum 3 files
    },
    fileFilter: (req, file, cb) => {
        // Accept images only
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('Only image files are allowed!'), false);
        }
    }
});

// Report issue endpoint
router.post('/report-issue', authenticateToken, upload.array('screenshots', 3), async (req, res) => {
    const client = await pool.connect();

    try {
        const { title, description, metadata } = req.body;
        const user = req.user;

        // Validate required fields
        if (!title || !description) {
            logger.warn('Missing required fields in issue report', { 
                hasTitle: !!title, 
                hasDescription: !!description,
                userId: user.id 
            });
            return res.fail({ statusCode: 400, message: 'Validation failed', errors: [{ message: 'Title and description are required' }] });
        }

        // Validate metadata format
        let parsedMetadata;
        try {
            parsedMetadata = JSON.parse(metadata);
        } catch (error) {
            logger.warn('Invalid metadata format in issue report', { 
                userId: user.id,
                error: error.message 
            });
            return res.fail({ statusCode: 400, message: 'Invalid metadata format', errors: [{ message: 'Invalid metadata format' }] });
        }

        await client.query('BEGIN');

        // Insert the report into the database
        const result = await client.query(
            'INSERT INTO issue_reports (user_id, title, description, metadata) VALUES ($1, $2, $3, $4) RETURNING id',
            [user.id, title, description, metadata]
        );
        const reportId = result.rows[0].id;

        // Handle file uploads if any
        if (req.files && req.files.length > 0) {
            for (const file of req.files) {
                try {
                    // Convert buffer to base64 with proper data URI format
                    const base64Data = `data:${file.mimetype};base64,${file.buffer.toString('base64')}`;
                    
                    const uploadResult = await cloudinary.uploader.upload(base64Data, {
                        resource_type: 'auto',
                        folder: 'issue_reports'
                    });

                    await client.query(
                        'INSERT INTO issue_report_attachments (issue_report_id, file_name, file_url) VALUES ($1, $2, $3)',
                        [reportId, file.originalname, uploadResult.secure_url]
                    );
                } catch (uploadError) {
                    logger.error('Error uploading file to Cloudinary:', {
                        error: uploadError.message,
                        reportId,
                        fileName: file.originalname,
                        mimetype: file.mimetype
                    });
                    throw new Error(`Failed to upload file: ${uploadError.message}`);
                }
            }
        }

        // Send email using EmailService
        try {
            await EmailService.sendIssueReport({
                title,
                description,
                user,
                metadata
            });
        } catch (emailError) {
            logger.error('Error sending issue report email:', {
                error: emailError.message,
                reportId,
                userId: user.id
            });
            // Don't throw here, as the report is already saved
        }

        await client.query('COMMIT');

        // Log the successful report
        logger.info('Issue report submitted successfully', {
            reportId,
            title,
            userId: user.id,
            userEmail: user.email,
            timestamp: new Date().toISOString()
        });

        res.success({ message: 'Issue report submitted successfully' });
    } catch (error) {
        await client.query('ROLLBACK');
        logger.error('Error submitting issue report:', {
            error: error.message,
            stack: error.stack,
            userId: req.user?.id,
            title: req.body?.title
        });
        res.fail({ statusCode: 500, message: 'Failed to submit issue report', errors: [{ message: process.env.NODE_ENV === 'development' ? error.message : 'Failed to submit issue report' }] });
    } finally {
        client.release();
    }
});

export default router; 