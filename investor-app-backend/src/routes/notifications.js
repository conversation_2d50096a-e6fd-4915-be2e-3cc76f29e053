import express from 'express';
import pg from 'pg';
import dotenv from 'dotenv';
import { authenticateToken } from '../middlewares/auth.js';

dotenv.config();

const router = express.Router();
const { Pool } = pg;
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

// ✅ **Fetch Notifications for Logged-in User**
router.get('/', authenticateToken, async (req, res) => {
    try {
        if (!req.user || !req.user.id) {
            return res.fail({ statusCode: 403, message: 'Invalid session', errors: [{ message: 'User ID missing in token' }] });
        }

        const userId = req.user.id;
        const includeIgnored = req.query.includeIgnored === 'true';
        const autoMarkAsRead = req.query.autoMarkAsRead !== 'false';

        const query = includeIgnored
            ? "SELECT * FROM notifications WHERE user_id = $1 ORDER BY created_at DESC"
            : "SELECT * FROM notifications WHERE status != 'ignored' AND user_id = $1 ORDER BY created_at DESC";

        const result = await pool.query(query, [userId]);

        let warnings = [];
        if (autoMarkAsRead) {
            const notificationsToMark = result.rows
                .filter(notification =>
                    notification.status === 'pending' &&
                    !['link_request', 'approval_request', 'action_required'].includes(notification.type)
                )
                .map(notification => notification.id);

            if (notificationsToMark.length > 0) {
                await pool.query(
                    `UPDATE notifications SET status = 'read' WHERE id = ANY($1) AND user_id = $2`,
                    [notificationsToMark, userId]
                );

                warnings.push({ message: `${notificationsToMark.length} notifications auto-marked as read` });
                const updatedResult = await pool.query(query, [userId]);
                return res.success({ data: { notifications: updatedResult.rows }, warnings });
            }
        }

        res.success({ data: { notifications: result.rows }, warnings });
    } catch (error) {
        res.fail({ statusCode: 500, message: 'Internal server error', errors: [{ message: error.message }] });
    }
});

// ✅ **Mark Multiple Notifications as Read**
router.put('/batch/mark-as-read', authenticateToken, async (req, res) => {
    try {
        const { ids } = req.body;
        const userId = req.user.id;

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.fail({ statusCode: 400, message: 'Invalid request', errors: [{ message: "'ids' must be a non-empty array" }] });
        }

        const result = await pool.query(
            'SELECT COUNT(*) FROM notifications WHERE id = ANY($1) AND user_id = $2',
            [ids, userId]
        );

        if (parseInt(result.rows[0].count) !== ids.length) {
            return res.fail({ statusCode: 403, message: 'Unauthorized', errors: [{ message: 'One or more notifications do not belong to the user' }] });
        }

        await pool.query(
            `UPDATE notifications SET status = 'read' WHERE id = ANY($1) AND user_id = $2`,
            [ids, userId]
        );

        res.success({ data: { updated: ids.length }, message: `${ids.length} notifications marked as read` });
    } catch (error) {
        res.fail({ statusCode: 500, message: 'Internal server error', errors: [{ message: error.message }] });
    }
});

// ✅ **Mark Notification as Read or Ignored**
router.put('/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        const userId = req.user.id;


        if (!['read', 'ignored'].includes(status)) {
            return res.fail({ statusCode: 400, message: 'Invalid status value', errors: [{ message: 'Invalid status value' }] });
        }

        // Ensure the notification belongs to the user
        const result = await pool.query(
            'SELECT * FROM notifications WHERE id = $1 AND user_id = $2',
            [id, userId]
        );

        if (result.rowCount === 0) {
            return res.fail({ statusCode: 403, message: 'Unauthorized', errors: [{ message: 'Notification does not belong to the user' }] });
        }

        // ✅ **Update Notification Status**
        await pool.query(
            `UPDATE notifications SET status = $1 WHERE id = $2`,
            [status, id]
        );

        res.success({ message: `Notification marked as ${status}` });
    } catch (error) {
        res.fail({ statusCode: 500, message: 'Internal server error', errors: [{ message: error.message }] });
    }
});

// ✅ **Approve a Link Request**
router.post('/confirm-link', authenticateToken, async (req, res) => {
    try {
        const { notificationId } = req.body;
        const clientId = req.user.id;

        const notificationCheck = await pool.query(
            `SELECT * FROM notifications WHERE id = $1 AND user_id = $2 AND type = 'link_request' AND status = 'pending'`,
            [notificationId, clientId]
        );

        if (notificationCheck.rows.length === 0) {
            return res.fail({ statusCode: 400, message: 'Invalid or expired link request', errors: [{ message: 'Invalid or expired link request' }] });
        }

        const managerId = notificationCheck.rows[0].sender_id;

        // ✅ **Link Client to Manager**
        await pool.query(
            `UPDATE institutional_investors SET manager_id = $1 WHERE id = $2`,
            [managerId, clientId]
        );

        // ✅ **Mark Notification as Approved**
        await pool.query(
            `UPDATE notifications SET status = 'approved' WHERE id = $1`,
            [notificationId]
        );

        // ✅ **Send Notification to Manager**
        await pool.query(
            `INSERT INTO notifications (user_id, type, message, status)
             VALUES ($1, 'approval', 'A client has accepted your link request.', 'pending')`,
            [managerId]
        );

        res.success({ message: 'Client successfully linked to manager.' });
    } catch (error) {
        res.fail({ statusCode: 500, message: 'Internal server error', errors: [{ message: error.message }] });
    }
});

// ✅ **Ignore a Link Request**
router.post('/ignore-link', authenticateToken, async (req, res) => {
    try {
        const { notificationId } = req.body;
        const clientId = req.user.id;

        if (!clientId) {
            return res.fail({ statusCode: 401, message: 'Unauthorized', errors: [{ message: 'No valid user session' }] });
        }


        // Ensure notification exists
        const notificationCheck = await pool.query(
            `SELECT * FROM notifications WHERE id = $1 AND user_id = $2 AND type = 'link_request' AND status = 'pending'`,
            [notificationId, clientId]
        );

        if (notificationCheck.rows.length === 0) {
            return res.fail({ statusCode: 400, message: 'Invalid or expired link request', errors: [{ message: 'Invalid or expired link request' }] });
        }

        // ✅ Mark the notification as "ignored"
        await pool.query(`UPDATE notifications SET status = 'ignored' WHERE id = $1`, [notificationId]);

        res.success({ message: `Link request ${notificationId} ignored successfully.` });
    } catch (error) {
        res.fail({ statusCode: 500, message: 'Internal server error', errors: [{ message: error.message }] });
    }
});

export default router;