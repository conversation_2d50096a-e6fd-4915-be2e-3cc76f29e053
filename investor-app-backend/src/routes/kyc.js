import express from 'express'
import multer from 'multer'
import { v4 as uuidv4 } from 'uuid'
import { authenticateToken } from '../middlewares/auth.js'
import { pool } from '../setup/databases.js'
import cloudinary from '../cloudinary/cloudinaryConfig.js'
import { logger } from '../utils/logger.js'
import { verifyKYC } from '../services/kycVerificationService.js'

const router = express.Router()

// Validate Cloudinary configuration
const validateCloudinaryConfig = () => {
  const requiredEnvVars = ['CLOUDINARY_CLOUD_NAME', 'CLOUDINARY_API_KEY', 'CLOUDINARY_API_SECRET']
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required Cloudinary environment variables: ${missingVars.join(', ')}`)
  }
}

// Configure multer for memory storage with better error handling
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 2 // Maximum number of files
  },
  fileFilter: (req, file, cb) => {
    // Accept images only
    if (!file.originalname.match(/\.(jpg|jpeg|png)$/)) {
      return cb(new Error('Only image files are allowed!'), false)
    }
    cb(null, true)
  }
}).fields([
  { name: 'selfie', maxCount: 1 },
  { name: 'passport', maxCount: 1 }
])

// KYC verification endpoint
router.post('/verify', authenticateToken, (req, res, next) => {
  logger.info('Starting KYC verification process...')
  upload(req, res, async (err) => {
    logger.info('Multer upload completed, checking for errors...')
    
    try {
      if (err instanceof multer.MulterError) {
        logger.error('Multer error:', err)
        return res.status(400).json({
          success: false,
          message: 'File upload error',
          details: err.message
        })
      } else if (err) {
        logger.error('Upload error:', err)
        return res.status(400).json({
          success: false,
          message: 'Invalid file upload',
          details: err.message
        })
      }

      // Check if files were uploaded
      if (!req.files || !req.files.selfie || !req.files.passport) {
        logger.error('Missing files:', { files: req.files })
        return res.status(400).json({
          success: false,
          message: 'Both selfie and passport images are required'
        })
      }

      const selfieFile = req.files.selfie[0]
      const passportFile = req.files.passport[0]

      logger.info('Files received:', {
        selfieSize: selfieFile.size,
        passportSize: passportFile.size,
        selfieType: selfieFile.mimetype,
        passportType: passportFile.mimetype
      })

      // Start transaction
      logger.info('Starting database transaction...')
      let client;
      
      try {
        client = await pool.connect()
        logger.info('Database connection established')
        
        await client.query('BEGIN')

        // Upload images to Cloudinary
        logger.info('Starting Cloudinary uploads...')
        const [selfieUrl, passportUrl] = await Promise.all([
          new Promise((resolve, reject) => {
            logger.info('Starting selfie upload to Cloudinary...')
            const stream = cloudinary.uploader.upload_stream(
              { 
                folder: 'kyc_verifications/selfies',
                public_id: `selfie_${req.user.id}_${Date.now()}`
              },
              (error, result) => {
                if (error) {
                  logger.error('Cloudinary selfie upload error:', error)
                  reject(error)
                  return
                }
                logger.info('Selfie upload successful:', { url: result.secure_url })
                resolve(result.secure_url)
              }
            )
            stream.end(selfieFile.buffer)
          }),
          new Promise((resolve, reject) => {
            logger.info('Starting passport upload to Cloudinary...')
            const stream = cloudinary.uploader.upload_stream(
              { 
                folder: 'kyc_verifications/passports',
                public_id: `passport_${req.user.id}_${Date.now()}`
              },
              (error, result) => {
                if (error) {
                  logger.error('Cloudinary passport upload error:', error)
                  reject(error)
                  return
                }
                logger.info('Passport upload successful:', { url: result.secure_url })
                resolve(result.secure_url)
              }
            )
            stream.end(passportFile.buffer)
          })
        ])

        logger.info('All Cloudinary uploads completed successfully')

        // Insert verification record
        logger.info('Inserting verification record into database...')
        const result = await client.query(
          `INSERT INTO kyc_verifications 
           (user_id, selfie_url, passport_url, status, submitted_at, updated_at)
           VALUES ($1, $2, $3, 'pending', NOW(), NOW())
           RETURNING id`,
          [req.user.id, selfieUrl, passportUrl]
        )

        // Perform KYC verification
        logger.info('Starting KYC verification process...')
        const verificationResult = await verifyKYC(selfieUrl, passportUrl)
        
        // Update verification status based on result
        const status = verificationResult.success ? 'verified' : 'failed'
        const metadata = verificationResult.details

        await client.query(
          `UPDATE kyc_verifications 
           SET status = $1, metadata = $2, updated_at = NOW()
           WHERE id = $3`,
          [status, metadata, result.rows[0].id]
        )

        // Update user's KYC status
        await client.query(
          `UPDATE institutional_investors 
           SET kyc_status = $1
           WHERE id = $2`,
          [status, req.user.id]
        )

        // Commit transaction
        logger.info('Committing database transaction...')
        await client.query('COMMIT')

        logger.info('KYC verification process completed successfully')
        res.success({
          data: {
            verificationId: result.rows[0].id,
            status: status,
            details: verificationResult.details
          },
          message: verificationResult.message
        })

      } catch (error) {
        // Rollback transaction on error
        logger.error('Error occurred during KYC verification:', error)
        if (client) {
          try {
            await client.query('ROLLBACK')
          } catch (rollbackError) {
            logger.error('Error rolling back transaction:', rollbackError)
          }
        }
        
        // Handle specific error cases
        if (error.message.includes('Cloudinary')) {
          logger.error('Cloudinary configuration error:', error)
          return res.fail({ statusCode: 500, message: 'Image upload service is not properly configured. Please contact support.', errors: [{ message: error.message }] })
        }

        if (error.code === 'ECONNREFUSED') {
          logger.error('Database connection error:', error)
          return res.fail({ statusCode: 500, message: 'Unable to connect to the database. Please try again later.', errors: [{ message: error.message }] })
        }

        res.fail({ statusCode: 500, message: 'Failed to submit verification. Please try again.', errors: [{ message: error.message }] })
      } finally {
        if (client) {
          try {
            client.release()
          } catch (releaseError) {
            logger.error('Error releasing database client:', releaseError)
          }
        }
      }
    } catch (error) {
      logger.error('Unexpected error during KYC verification:', error)
      res.fail({ statusCode: 500, message: 'An unexpected error occurred. Please try again.', errors: [{ message: error.message }] })
    }
  })
})

// Get KYC status endpoint
router.get('/status', authenticateToken, async (req, res) => {
  try {
    const query = `
      SELECT kv.status, kv.submitted_at, kv.updated_at, ii.kyc_status
      FROM kyc_verifications kv
      JOIN institutional_investors ii ON kv.user_id = ii.id
      WHERE kv.user_id = $1
      ORDER BY kv.submitted_at DESC
      LIMIT 1
    `

    const result = await pool.query(query, [req.user.id])

    if (result.rows.length === 0) {
      return res.success({ data: { status: 'not_submitted', kyc_status: 'pending' } });
    }

    res.success({ data: result.rows[0] });
  } catch (error) {
    console.error('Error fetching KYC status:', error)
    res.fail({ statusCode: 500, message: 'Failed to fetch KYC status', errors: [{ message: error.message }] });
  }
})

export default router
