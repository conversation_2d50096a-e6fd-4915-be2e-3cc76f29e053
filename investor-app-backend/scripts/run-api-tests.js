import fs from 'fs';
import path from 'path';

const env = process.argv[2] || 'dev';
const configPath = path.join('tests', 'endpoints.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));

const baseURL = env === 'prod' ? config.productionBaseURL : config.developmentBaseURL;

async function runTest(ep) {
  const url = baseURL + ep.path;
  const options = { method: ep.method || 'GET', headers: { 'Content-Type': 'application/json', ...(ep.headers || {}) } };
  if (ep.body) options.body = JSON.stringify(ep.body);
  const start = Date.now();
  try {
    const res = await fetch(url, options);
    const duration = Date.now() - start;
    const ok = (ep.expectedStatus || [200]).includes(res.status);
    return { name: ep.name, status: res.status, duration, ok };
  } catch (err) {
    return { name: ep.name, error: err.message, ok: false };
  }
}

async function main() {
  const results = [];
  for (const ep of config.endpoints) {
    // eslint-disable-next-line no-await-in-loop
    const r = await runTest(ep);
    results.push(r);
  }

  console.log(`Testing ${env} environment at ${baseURL}`);
  let passed = 0;
  for (const r of results) {
    if (r.ok) {
      passed += 1;
      console.log(`\x1b[32m✓\x1b[0m ${r.name} (${r.status}) - ${r.duration}ms`);
    } else if (r.error) {
      console.log(`\x1b[31m✗ ${r.name} - Error: ${r.error}\x1b[0m`);
    } else {
      console.log(`\x1b[31m✗ ${r.name} - Status ${r.status}\x1b[0m`);
    }
  }
  console.log(`\nSummary: ${passed}/${results.length} passed`);
  if (passed !== results.length) process.exitCode = 1;
}

main();
